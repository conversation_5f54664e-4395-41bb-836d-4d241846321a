<?php

namespace Tests\Feature;

use App\Models\Subscription;
use App\Repositories\SubscriptionsRepository;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SubscriptionsRepositoryTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription is near expiry (within 7 days).
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_subscription_near_expiry()
    {
        // Arrange - Create a subscription that expires in 5 days
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDays(5);

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription is not near expiry (more than 7 days left).
     */
    public function test_isSubscriptionNearExpiry_returns_false_for_subscription_not_near_expiry()
    {
        // Arrange - Create a subscription that expires in 10 days
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDays(10);

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription has already expired.
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_expired_subscription()
    {
        // Arrange - Create a subscription that expired yesterday
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->subDay();

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription expiration date is exactly 7 days away.
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_exactly_7_days_left()
    {
        // Arrange - Create a subscription that expires in exactly 7 days
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDays(7);

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription expires today.
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_subscription_expiring_today()
    {
        // Arrange - Create a subscription that expires today
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today();

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription expires in exactly 8 days (boundary test).
     */
    public function test_isSubscriptionNearExpiry_returns_false_for_exactly_8_days_left()
    {
        // Arrange - Create a subscription that expires in exactly 8 days
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDays(8);

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * when the subscription expires in 1 day.
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_subscription_expiring_tomorrow()
    {
        // Arrange - Create a subscription that expires tomorrow
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDay();

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * with different time formats to ensure robust parsing.
     */
    public function test_isSubscriptionNearExpiry_handles_different_date_formats()
    {
        // Arrange - Create a subscription with ends_at as a string
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->addDays(3)->toDateString();

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test the isSubscriptionNearExpiry method
     * with a subscription that expired multiple days ago.
     */
    public function test_isSubscriptionNearExpiry_returns_true_for_subscription_expired_multiple_days_ago()
    {
        // Arrange - Create a subscription that expired 5 days ago
        $subscription = new Subscription();
        $subscription->ends_at = Carbon::today()->subDays(5);

        // Act
        $result = SubscriptionsRepository::isSubscriptionNearExpiry($subscription);

        // Assert
        $this->assertTrue($result);
    }
}
