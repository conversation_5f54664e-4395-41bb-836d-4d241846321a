<?php

namespace Tests\Feature\Commands;

use App\Mail\PaymentReminderEmail;
use App\Models\Partner;
use App\Models\Tenant;
use App\Models\TradeDoc;
use App\Models\User;
use App\Repositories\TenantRepository;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class SendPaymentRemindersCommandTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    public function test_command_shows_help_information()
    {
        $this->artisan('app:send-payment-reminders --help')
            ->expectsOutputToContain('Send payment reminder emails for upcoming invoice due dates')
            ->assertExitCode(0);
    }

    public function test_dry_run_shows_statistics_without_sending_emails()
    {
        // Create test data
        $this->createTestTradeDocWithReminder();

        $this->artisan('app:send-payment-reminders --dry-run')
            ->expectsOutput('Starting payment reminder process...')
            ->expectsOutput('DRY RUN MODE - No emails will be sent')
            ->expectsOutput('Dry run completed. No emails were sent.')
            ->assertExitCode(0);

        // Verify no emails were sent
        Mail::assertNothingSent();
    }

    public function test_command_sends_payment_reminders_for_eligible_invoices()
    {
        // Create test data
        $tradeDoc = $this->createTestTradeDocWithReminder();

        $this->artisan('app:send-payment-reminders')
            ->expectsOutput('Starting payment reminder process...')
            ->expectsOutput('Found 1 invoice(s) requiring payment reminders')
            ->assertExitCode(0);

        // Verify email was queued
        Mail::assertQueued(PaymentReminderEmail::class);

//        Mail::assertSent(PaymentReminderEmail::class, function ($mail) use ($tradeDoc) {
//            return $mail->tradeDoc->uuid === $tradeDoc->uuid;
//        });

        // Verify reminder was marked as sent
        $tradeDoc->refresh();
        $this->assertNotNull($tradeDoc->payment_reminder_sent_at);
    }

    public function test_command_skips_invoices_without_buyer_email()
    {
        // Create trade doc with buyer without email
        $tenant = Tenant::factory()->create();
        $user = User::factory()->create();
        $buyer = Partner::factory()->forTenant($tenant)->create(['email' => null]);

        TradeDoc::factory()->create([
            'installation' => $tenant->id,
            'issuer_id' => $tenant->id,
            'buyer_id' => $buyer->id,
            'creator_id' => $user->id,
            'payment_reminder_enabled' => true,
            'payment_reminder_days' => 3,
            'payment_due_date' => now()->addDays(2),
            'is_paid' => false,
            'payment_reminder_sent_at' => null,
        ]);

        $this->artisan('app:send-payment-reminders')
            ->expectsOutput('Found 0 invoice(s) requiring payment reminders')
            ->assertExitCode(0);

        Mail::assertNothingSent();
    }

    public function test_command_skips_already_paid_invoices()
    {
        // Create paid trade doc
        $tradeDoc = $this->createTestTradeDocWithReminder([
            'is_paid' => true,
        ]);

        $this->artisan('app:send-payment-reminders')
            ->expectsOutput('Found 0 invoice(s) requiring payment reminders')
            ->assertExitCode(0);

        Mail::assertNothingSent();
    }

    public function test_command_skips_invoices_with_already_sent_reminders()
    {
        // Create trade doc with already sent reminder
        $tradeDoc = $this->createTestTradeDocWithReminder([
            'payment_reminder_sent_at' => now()->subDay(),
        ]);

        $this->artisan('app:send-payment-reminders')
            ->expectsOutput('Found 0 invoice(s) requiring payment reminders')
            ->assertExitCode(0);

        Mail::assertNothingSent();
    }

    public function test_command_respects_tenant_email_limits()
    {
        // Mock tenant email limit reached
        $tradeDoc = $this->createTestTradeDocWithReminder();

        // Mock the TenantRepository to return false for email limit
        $this->mock(TenantRepository::class, function ($mock) {
            $mock->shouldReceive('canTenantSendEmails')->andReturn(false);
        });

        $this->artisan('app:send-payment-reminders')
            ->expectsOutput('Starting payment reminder process...')
            ->assertExitCode(0);

        Mail::assertNothingSent();
    }

    private function createTestTradeDocWithReminder(array $overrides = []): TradeDoc
    {
        $tenant = Tenant::factory()->create();
        $user = User::factory()->create();
        $buyer = Partner::factory()->forTenant($tenant)->create(['email' => '<EMAIL>']);

        return TradeDoc::factory()->create(array_merge([
            'installation' => $tenant->id,
            'issuer_id' => $tenant->id,
            'buyer_id' => $buyer->id,
            'creator_id' => $user->id,
            'payment_reminder_enabled' => true,
            'payment_reminder_days' => 1,
            'payment_due_date' => now()->addDay(), // Due tomorrow, reminder should be sent today (1 day before)
            'is_paid' => false,
            'payment_reminder_sent_at' => null,
        ], $overrides));
    }
}
