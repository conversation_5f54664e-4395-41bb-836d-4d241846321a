<?php

namespace Tests\Feature;

use App\Enums\LegalDocumentStatus;
use App\Enums\LegalDocumentType;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class LegalDocumentTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_can_create_legal_document(): void
    {
        $data = [
            'title' => 'Test Terms of Service',
            'content' => 'This is a test terms of service document.',
            'document_type' => LegalDocumentType::TOS,
        ];

        $document = LegalDocumentRepository::createDraft($data);

        $this->assertInstanceOf(LegalDocument::class, $document);
        $this->assertEquals($data['title'], $document->title);
        $this->assertEquals($data['content'], $document->content);
        $this->assertEquals($data['document_type'], $document->document_type);
        $this->assertEquals(LegalDocumentStatus::DRAFT, $document->status);
    }

    public function test_can_publish_draft_document(): void
    {
        $document = LegalDocument::factory()
            ->tos()
            ->create([]);

        $this->assertTrue($document->canBePublished());

        $result = LegalDocumentRepository::publishDraft($document);

        $this->assertTrue($result);
        $document->refresh();
        $this->assertEquals(LegalDocumentStatus::PUBLISHED, $document->status);
        $this->assertNotNull($document->publication_date);
        $this->assertNotNull($document->html_file_path);
        $this->assertNotNull($document->pdf_file_path);
        $document->deleteFiles();
    }

    public function test_publishing_archives_existing_published_document(): void
    {
        $existingDocument = LegalDocument::factory()
            ->tos()
            ->published()
            ->create([]);

        $newDocument = LegalDocument::factory()
            ->tos()
            ->version(2)
            ->create([]);

        LegalDocumentRepository::publishDraft($newDocument);

        $existingDocument->refresh();
        $this->assertEquals(LegalDocumentStatus::ARCHIVED, $existingDocument->status);

        $newDocument->refresh();
        $this->assertEquals(LegalDocumentStatus::PUBLISHED, $newDocument->status);
    }

    public function test_can_clone_published_document(): void
    {
        $publishedDocument = LegalDocument::factory()
            ->tos()
            ->published()
            ->create([]);

        $clonedDocument = LegalDocumentRepository::cloneToNewDraft($publishedDocument);

        $this->assertInstanceOf(LegalDocument::class, $clonedDocument);
        $this->assertEquals($publishedDocument->title, $clonedDocument->title);
        $this->assertEquals($publishedDocument->content, $clonedDocument->content);
        $this->assertEquals($publishedDocument->document_type, $clonedDocument->document_type);
        $this->assertEquals(LegalDocumentStatus::DRAFT, $clonedDocument->status);
        $this->assertEquals($publishedDocument->version_number + 1, $clonedDocument->version_number);
        $this->assertNull($clonedDocument->publication_date);
    }

    public function test_can_get_current_published_document(): void
    {
        // Create multiple documents of the same type
        LegalDocument::factory()
            ->tos()
            ->archived()
            ->version(1)
            ->create([]);

        $publishedDocument = LegalDocument::factory()
            ->tos()
            ->published()
            ->version(2)
            ->create([]);

        LegalDocument::factory()
            ->tos()
            ->version(3)
            ->create([]); // Draft

        $currentPublished = LegalDocumentRepository::getCurrentPublished(
            LegalDocumentType::TOS
        );

        $this->assertNotNull($currentPublished);
        $this->assertEquals(LegalDocumentStatus::PUBLISHED, $currentPublished->status);
    }

    public function test_version_numbers_increment_correctly(): void
    {
        $lastTOSVersionNumber  = LegalDocumentRepository::getNextVersionNumber(LegalDocumentType::TOS);

        $doc1 = LegalDocument::factory()
            ->tos()
            ->create([]);
        $this->assertEquals($lastTOSVersionNumber, $doc1->version_number);

        // Create second document of same type
        $data = [
            'title' => 'Test Terms v2',
            'content' => 'Updated content',
            'document_type' => LegalDocumentType::TOS,
        ];
        $doc2 = LegalDocumentRepository::createDraft($data);
        $this->assertEquals($lastTOSVersionNumber + 1, $doc2->version_number);

        // Create document of different type - should start at 1
        $lastGDPRVersionNumber  = LegalDocumentRepository::getNextVersionNumber(LegalDocumentType::GDPR);
        $data['document_type'] = LegalDocumentType::GDPR;
        $doc3 = LegalDocumentRepository::createDraft($data);
        $this->assertEquals($lastGDPRVersionNumber, $doc3->version_number);
    }

    public function test_only_one_published_document_per_type(): void
    {
        $doc1 = LegalDocument::factory()
            ->tos()
            ->create([]);
        LegalDocumentRepository::publishDraft($doc1);

        $doc2 = LegalDocument::factory()
            ->tos()
            ->version(2)
            ->create([]);
        LegalDocumentRepository::publishDraft($doc2);

        $publishedTosCount = LegalDocument::where('document_type', LegalDocumentType::TOS)
            ->where('status', LegalDocumentStatus::PUBLISHED)
            ->count();

        $this->assertEquals(1, $publishedTosCount);

        $doc2->refresh();
        $this->assertEquals(LegalDocumentStatus::PUBLISHED, $doc2->status);

        $doc1->refresh();
        $this->assertEquals(LegalDocumentStatus::ARCHIVED, $doc1->status);
    }

    public function test_can_generate_html_and_pdf(): void
    {
        Storage::fake('public');

        $document = LegalDocument::factory()
            ->tos()
            ->create([]);

        $html = LegalDocumentRepository::renderHtml($document);
        $this->assertStringContainsString($document->title, $html);

        $this->assertStringContainsString('TERMS OF SERVICE<br />', $html);
        $this->assertStringContainsString('1. ACCEPTANCE OF TERMS<br />', $html);

        // Test PDF generation
        $pdf = LegalDocumentRepository::generatePdf($document);
        $this->assertNotNull($pdf);
    }
}
