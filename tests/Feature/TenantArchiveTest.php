<?php

namespace Tests\Feature;

use App\Models\Payment;
use App\Models\PaymentWebhook;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Models\User;
use App\Repositories\TenantRepository;
use App\Services\TenantArchiverService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Laravel\Nightwatch\Facades\Nightwatch;
use Tests\TestCase;

class TenantArchiveTest extends TestCase
{
    use DatabaseTransactions;

    protected TenantArchiverService $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new TenantArchiverService();
        // Use fake storage for testing
        Storage::fake('local');
    }

    /** @test */
    public function it_can_archive_tenant_data_successfully()
    {
        // Create test tenant with metadata using factory
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-hash-123',
            'name' => 'Test Tenant Company'
        ]);

        // Create test users with profiles
        $user1 = User::factory()->withProfile()->create(['name' => 'Test User 1']);
        $user2 = User::factory()->withProfile()->create(['name' => 'Test User 2']);

        // Associate users with tenant
        $tenant->user()->attach([$user1->id, $user2->id]);

        // Create test plan and subscriptions
        $plan = Plan::factory()->create(['name' => 'Test Plan']);

        $subscription1 = Subscription::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user1->id,
            'plan_id' => $plan->id
        ]);

        $subscription2 = Subscription::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user2->id,
            'plan_id' => $plan->id
        ]);

        // Create test payments with webhooks
        $payment1 = Payment::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user1->id,
            'subscription_id' => $subscription1->id
        ]);

        $payment2 = Payment::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user2->id,
            'subscription_id' => $subscription2->id
        ]);

        // Create payment webhooks manually
        PaymentWebhook::create([
            'payment_id' => $payment1->id,
            'payload' => ['test' => 'webhook_data_1']
        ]);

        PaymentWebhook::create([
            'payment_id' => $payment2->id,
            'payload' => ['test' => 'webhook_data_2']
        ]);

        // Execute the archive method
        $result = $this->repository->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        // Assert archive directory was created
        $archiveDir = $this->repository->getArchiveDir($tenant);
        Storage::disk('local')->assertExists($archiveDir);

        // Assert all JSON files were created
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertExists("{$archiveDir}/users.json");
        Storage::disk('local')->assertExists("{$archiveDir}/subscriptions.json");
        Storage::disk('local')->assertExists("{$archiveDir}/payments.json");

        // Verify tenant.json content
        $tenantJson = json_decode(Storage::disk('local')->get("{$archiveDir}/tenant.json"), true);
        $this->assertArrayHasKey('archived_at', $tenantJson);
        $this->assertArrayHasKey('archive_version', $tenantJson);
        $this->assertArrayHasKey('tenant_data', $tenantJson);
        $this->assertEquals('Test Tenant Company', $tenantJson['tenant_data']['name']);
        $this->assertArrayHasKey('meta', $tenantJson['tenant_data']);

        // Verify users.json content
        $usersJson = json_decode(Storage::disk('local')->get("{$archiveDir}/users.json"), true);
        $this->assertArrayHasKey('total_users', $usersJson);
        $this->assertEquals(2, $usersJson['total_users']);
        $this->assertArrayHasKey('users_data', $usersJson);

        // Verify sensitive data is excluded
        foreach ($usersJson['users_data'] as $userData) {
            $this->assertArrayNotHasKey('password', $userData);
            $this->assertArrayNotHasKey('remember_token', $userData);
            $this->assertArrayHasKey('profile_excluded', $userData);
        }

        // Verify subscriptions.json content
        $subscriptionsJson = json_decode(Storage::disk('local')->get("{$archiveDir}/subscriptions.json"), true);
        $this->assertArrayHasKey('total_subscriptions', $subscriptionsJson);
        $this->assertEquals(2, $subscriptionsJson['total_subscriptions']);
        $this->assertArrayHasKey('subscriptions_data', $subscriptionsJson);

        // Verify payments.json content
        $paymentsJson = json_decode(Storage::disk('local')->get("{$archiveDir}/payments.json"), true);
        $this->assertArrayHasKey('total_payments', $paymentsJson);
        $this->assertEquals(2, $paymentsJson['total_payments']);
        $this->assertArrayHasKey('payments_data', $paymentsJson);

        // Verify webhook data is included
        foreach ($paymentsJson['payments_data'] as $paymentData) {
            $this->assertArrayHasKey('webhooks_data', $paymentData);
        }
    }

    /** @test */
    public function it_handles_tenant_without_metadata_gracefully()
    {
        // Create tenant without metadata
        $tenant = Tenant::factory()->create([
            'hash' => 'tenant-no-meta-456',
            'name' => 'Tenant Without Meta'
        ]);

        // Execute the archive method
        $result = $this->repository->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        // Verify tenant.json was created without metadata
        $archiveDir = $this->repository->getArchiveDir($tenant);
        $tenantJson = json_decode(Storage::disk('local')->get("{$archiveDir}/tenant.json"), true);
        $this->assertArrayHasKey('meta', $tenantJson['tenant_data']);
    }

    /** @test */
    public function it_handles_tenant_without_related_data_gracefully()
    {
        // Create tenant with no users, subscriptions, or payments
        $tenant = Tenant::factory()->create([
            'hash' => 'tenant-empty-789',
            'name' => 'Empty Tenant'
        ]);

        // Execute the archive method
        $result = $this->repository->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        // Verify all files were created with empty data
        $archiveDir = $this->repository->getArchiveDir($tenant);

        $usersJson = json_decode(Storage::disk('local')->get("{$archiveDir}/users.json"), true);
        $this->assertEquals(0, $usersJson['total_users']);

        $subscriptionsJson = json_decode(Storage::disk('local')->get("{$archiveDir}/subscriptions.json"), true);
        $this->assertEquals(0, $subscriptionsJson['total_subscriptions']);

        $paymentsJson = json_decode(Storage::disk('local')->get("{$archiveDir}/payments.json"), true);
        $this->assertEquals(0, $paymentsJson['total_payments']);
    }

    /** @test */
    public function it_archives_data_without_encryption_by_default_in_testing()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-no-encryption',
            'name' => 'Test Tenant No Encryption'
        ]);

        // Create test user
        $user = User::factory()->withProfile()->create(['name' => 'Test User']);
        $tenant->user()->attach([$user->id]);

        // Execute archive without explicitly setting encryption
        $result = $this->repository->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        // Assert files are created without .enc extension (not encrypted)
        $archiveDir = $this->repository->getArchiveDir($tenant);
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertExists("{$archiveDir}/users.json");

        // Assert .enc files do not exist
        Storage::disk('local')->assertMissing("{$archiveDir}/tenant.json.enc");
        Storage::disk('local')->assertMissing("{$archiveDir}/users.json.enc");

        // Verify content is readable JSON (not encrypted)
        $tenantJson = json_decode(Storage::disk('local')->get("{$archiveDir}/tenant.json"), true);
        $this->assertIsArray($tenantJson);
        $this->assertArrayHasKey('tenant_data', $tenantJson);
        $this->assertEquals('Test Tenant No Encryption', $tenantJson['tenant_data']['name']);
    }

    /** @test */
    public function it_archives_data_with_encryption_when_explicitly_enabled()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-encrypted',
            'name' => 'Test Tenant Encrypted'
        ]);

        // Create test user
        $user = User::factory()->withProfile()->create(['name' => 'Test User Encrypted']);
        $tenant->user()->attach([$user->id]);

        // Execute archive with encryption enabled
        $result = $this->repository->encrypted(true)->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        // Assert encrypted files are created with .enc extension
        $archiveDir = $this->repository->getArchiveDir($tenant);
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json.enc");
        Storage::disk('local')->assertExists("{$archiveDir}/users.json.enc");
        Storage::disk('local')->assertExists("{$archiveDir}/subscriptions.json.enc");
        Storage::disk('local')->assertExists("{$archiveDir}/payments.json.enc");

        // Assert non-encrypted files do not exist
        Storage::disk('local')->assertMissing("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertMissing("{$archiveDir}/users.json");

        // Verify content is encrypted (not readable as plain JSON)
        $encryptedContent = Storage::disk('local')->get("{$archiveDir}/tenant.json.enc");
        $this->assertIsString($encryptedContent);

        // Attempting to decode as JSON should fail
        $this->assertNull(json_decode($encryptedContent, true));
    }

    /** @test */
    public function it_can_decrypt_encrypted_archive_files()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-decrypt',
            'name' => 'Test Tenant Decrypt'
        ]);

        // Create test user with profile
        $user = User::factory()->withProfile()->create(['name' => 'Test User Decrypt']);
        $tenant->user()->attach([$user->id]);

        // Execute archive with encryption enabled
        $result = $this->repository->encrypted(true)->archiveTenantData($tenant);
        $this->assertTrue($result);

        // Get encrypted content
        $archiveDir = $this->repository->getArchiveDir($tenant);
        $encryptedTenantContent = Storage::disk('local')->get("{$archiveDir}/tenant.json.enc");
        $encryptedUsersContent = Storage::disk('local')->get("{$archiveDir}/users.json.enc");

        // Decrypt the content
        $decryptedTenantContent = Crypt::decryptString($encryptedTenantContent);
        $decryptedUsersContent = Crypt::decryptString($encryptedUsersContent);

        // Verify decrypted content is valid JSON
        $tenantData = json_decode($decryptedTenantContent, true);
        $usersData = json_decode($decryptedUsersContent, true);

        $this->assertIsArray($tenantData);
        $this->assertIsArray($usersData);

        // Verify decrypted data contains expected information
        $this->assertArrayHasKey('tenant_data', $tenantData);
        $this->assertEquals('Test Tenant Decrypt', $tenantData['tenant_data']['name']);

        $this->assertArrayHasKey('users_data', $usersData);
        $this->assertEquals(1, $usersData['total_users']);
        $this->assertEquals('Test User Decrypt', $usersData['users_data'][0]['name']);
    }

    /** @test */
    public function it_can_toggle_encryption_on_and_off()
    {
        // Create test tenant
        $tenant = Tenant::factory()->create([
            'hash' => 'test-tenant-toggle',
            'name' => 'Test Tenant Toggle'
        ]);

        // First archive with encryption disabled
        $result1 = $this->repository->encrypted(false)->archiveTenantData($tenant);
        $this->assertTrue($result1);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Verify non-encrypted files exist
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertMissing("{$archiveDir}/tenant.json.enc");

        // Clean up for second test
        Storage::disk('local')->deleteDirectory($archiveDir);

        // Second archive with encryption enabled
        $result2 = $this->repository->encrypted(true)->archiveTenantData($tenant);
        $this->assertTrue($result2);

        // Verify encrypted files exist
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json.enc");
        Storage::disk('local')->assertMissing("{$archiveDir}/tenant.json");
    }

    /** @test */
    public function it_handles_encryption_errors_gracefully()
    {
        // Create test tenant
        $tenant = Tenant::factory()->create([
            'hash' => 'test-tenant-error',
            'name' => 'Test Tenant Error'
        ]);
        // Mock Crypt facade to throw an exception
        Crypt::shouldReceive('encryptString')
            ->once()
            ->andThrow(new \Exception('Encryption failed'));

        // Execute archive with encryption enabled
        $result = $this->repository->encrypted(true)->archiveTenantData($tenant);

        // Assert failure due to encryption error
        $this->assertFalse($result);
    }

    /** @test */
    public function it_can_compress_archive_without_removing_source_files()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-compress',
            'name' => 'Test Tenant Compress'
        ]);

        // Create test user
        $user = User::factory()->withProfile()->create(['name' => 'Test User Compress']);
        $tenant->user()->attach([$user->id]);

        // Execute archive with compression enabled but keep source files
        $result = $this->repository->compressed(true, false)->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Assert ZIP file was created
        Storage::disk('local')->assertExists("{$archiveDir}.zip");

        // Assert source files still exist (not removed when removeSourceFiles=false)
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertExists("{$archiveDir}/users.json");
        Storage::disk('local')->assertExists("{$archiveDir}/subscriptions.json");
        Storage::disk('local')->assertExists("{$archiveDir}/payments.json");

        // Verify ZIP file is not empty and contains expected files
        $zipPath = Storage::disk('local')->path("{$archiveDir}.zip");
        $this->assertGreaterThan(0, filesize($zipPath));

        // Verify ZIP contents
        $zip = new \ZipArchive();
        $this->assertTrue($zip->open($zipPath) === TRUE);
        $this->assertEquals(4, $zip->numFiles); // tenant, users, subscriptions, payments
        $zip->close();
    }

    /** @test */
    public function it_can_compress_archive_and_remove_source_files()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-compress-remove',
            'name' => 'Test Tenant Compress Remove'
        ]);

        // Create test user
        $user = User::factory()->withProfile()->create(['name' => 'Test User Compress Remove']);
        $tenant->user()->attach([$user->id]);

        // Execute archive with compression enabled and remove source files
        $result = $this->repository->compressed(true, true)->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Assert ZIP file was created
        Storage::disk('local')->assertExists("{$archiveDir}.zip");

        // Assert source directory was removed
        Storage::disk('local')->assertMissing($archiveDir);
        Storage::disk('local')->assertMissing("{$archiveDir}/tenant.json");
        Storage::disk('local')->assertMissing("{$archiveDir}/users.json");

        // Verify ZIP file is not empty
        $zipPath = Storage::disk('local')->path("{$archiveDir}.zip");
        $this->assertGreaterThan(0, filesize($zipPath));
    }

    /** @test */
    public function it_can_combine_encryption_and_compression()
    {
        // Create test tenant
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-encrypt-compress',
            'name' => 'Test Tenant Encrypt Compress'
        ]);

        // Create test user
        $user = User::factory()->withProfile()->create(['name' => 'Test User Encrypt Compress']);
        $tenant->user()->attach([$user->id]);

        // Execute archive with both encryption and compression enabled
        $result = $this->repository
            ->encrypted(true)
            ->compressed(true, true)
            ->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Assert ZIP file was created
        Storage::disk('local')->assertExists("{$archiveDir}.zip");

        // Assert source directory was removed (due to removeSourceFiles=true)
        Storage::disk('local')->assertMissing($archiveDir);

        // Verify ZIP file contains encrypted files by extracting and checking
        $zipPath = Storage::disk('local')->path("{$archiveDir}.zip");
        $extractDir = storage_path('app/test_extract_' . uniqid());

        $zip = new \ZipArchive();
        $zip->open($zipPath);
        $zip->extractTo($extractDir);
        $zip->close();

        // Check that extracted files have .enc extension
        $this->assertFileExists("{$extractDir}/tenant.json.enc");
        $this->assertFileExists("{$extractDir}/users.json.enc");

        // Verify content is encrypted
        $encryptedContent = file_get_contents("{$extractDir}/tenant.json.enc");
        $this->assertNull(json_decode($encryptedContent, true));

        // Clean up extracted files
        $this->deleteDirectory($extractDir);
    }

    /** @test */
    public function it_validates_zip_file_contents()
    {
        // Create test tenant with some data
        $tenant = Tenant::factory()->withMetadata()->create([
            'hash' => 'test-tenant-zip-contents',
            'name' => 'Test Tenant ZIP Contents'
        ]);

        $user = User::factory()->withProfile()->create(['name' => 'Test User ZIP']);
        $tenant->user()->attach([$user->id]);

        // Create subscription and payment for more files
        $plan = Plan::factory()->create(['name' => 'Test Plan']);
        $subscription = Subscription::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user->id,
            'plan_id' => $plan->id
        ]);

        $payment = Payment::factory()->create([
            'tenant_id' => $tenant->id,
            'user_id' => $user->id,
            'subscription_id' => $subscription->id
        ]);

        // Execute archive with compression
        $result = $this->repository->compressed(true, false)->archiveTenantData($tenant);
        $this->assertTrue($result);

        $archiveDir = $this->repository->getArchiveDir($tenant);
        $zipPath = Storage::disk('local')->path("{$archiveDir}.zip");

        // Extract and verify ZIP contents
        $extractDir = storage_path('app/test_extract_' . uniqid());

        $zip = new \ZipArchive();
        $this->assertTrue($zip->open($zipPath) === TRUE);

        // Check number of files in ZIP
        $this->assertEquals(4, $zip->numFiles); // tenant, users, subscriptions, payments

        // Check specific files exist in ZIP
        $this->assertNotFalse($zip->locateName('tenant.json'));
        $this->assertNotFalse($zip->locateName('users.json'));
        $this->assertNotFalse($zip->locateName('subscriptions.json'));
        $this->assertNotFalse($zip->locateName('payments.json'));

        $zip->extractTo($extractDir);
        $zip->close();

        // Verify extracted file contents
        $tenantData = json_decode(file_get_contents("{$extractDir}/tenant.json"), true);
        $this->assertEquals('Test Tenant ZIP Contents', $tenantData['tenant_data']['name']);

        $usersData = json_decode(file_get_contents("{$extractDir}/users.json"), true);
        $this->assertEquals(1, $usersData['total_users']);

        // Clean up
        $this->deleteDirectory($extractDir);
    }

    /**
     * Helper method to recursively delete directory
     */
    private function deleteDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            is_dir($path) ? $this->deleteDirectory($path) : unlink($path);
        }

        return rmdir($dir);
    }

    /** @test */
    public function it_archives_without_compression_by_default()
    {
        // Create test tenant
        $tenant = Tenant::factory()->create([
            'hash' => 'test-tenant-no-compress',
            'name' => 'Test Tenant No Compress'
        ]);

        // Execute archive without compression
        $result = $this->repository->archiveTenantData($tenant);

        // Assert success
        $this->assertTrue($result);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Assert ZIP file was NOT created
        Storage::disk('local')->assertMissing("{$archiveDir}.zip");

        // Assert source directory exists
        Storage::disk('local')->assertExists($archiveDir);
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");
    }

    /** @test */
    public function it_can_toggle_compression_settings()
    {
        // Create test tenant
        $tenant = Tenant::factory()->create([
            'hash' => 'test-tenant-toggle-compress',
            'name' => 'Test Tenant Toggle Compress'
        ]);

        // First archive without compression
        $result1 = $this->repository->compressed(false)->archiveTenantData($tenant);
        $this->assertTrue($result1);

        $archiveDir = $this->repository->getArchiveDir($tenant);

        // Verify no ZIP file created
        Storage::disk('local')->assertMissing("{$archiveDir}.zip");
        Storage::disk('local')->assertExists($archiveDir);

        // Clean up for second test
        Storage::disk('local')->deleteDirectory($archiveDir);

        // Second archive with compression enabled but keep source files
        $result2 = $this->repository->compressed(true, false)->archiveTenantData($tenant);
        $this->assertTrue($result2);

        // Verify ZIP file created and source files remain
        Storage::disk('local')->assertExists("{$archiveDir}.zip");
        Storage::disk('local')->assertExists($archiveDir);
        Storage::disk('local')->assertExists("{$archiveDir}/tenant.json");

        // Clean up for third test
        Storage::disk('local')->deleteDirectory($archiveDir);
        Storage::disk('local')->delete("{$archiveDir}.zip");

        // Third archive with compression enabled and remove source files
        $result3 = $this->repository->compressed(true, true)->archiveTenantData($tenant);
        $this->assertTrue($result3);

        // Verify ZIP file created and source files removed
        Storage::disk('local')->assertExists("{$archiveDir}.zip");
        Storage::disk('local')->assertMissing($archiveDir);
    }
}
