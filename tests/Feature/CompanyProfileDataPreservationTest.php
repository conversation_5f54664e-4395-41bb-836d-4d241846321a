<?php

namespace Tests\Feature;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\Roles;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Filament\App\Pages\CompanyProfile;
use App\Models\Company;
use App\Models\DTOTenantMetadata;
use App\Models\ProfileData;
use App\Models\Tenant;
use App\Models\TenantMeta;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Livewire\Livewire;
use Tests\TestCase;

class CompanyProfileDataPreservationTest extends TestCase
{
    use DatabaseTransactions;

    private User $user;
    private Tenant $tenant;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test tenant with complete metadata including invoice configuration
        $this->tenant = Tenant::factory()->create([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'vat_id' => '123-456-78-90',
            'tax_residency_country' => TaxResidencyCountries::PL,
            'business_type' => PartnerBusinessTypes::INDIVIDUAL,
            'vat_type' => PartnerVATTypes::LOCAL,
            'tax_type' => TaxTypePL::LINEAR,
            'accounting_type' => AccountingTypesPL::FULL,
        ]);

        // Create user and profile
        $this->user = User::factory()->create();

        // Attach user to tenant using the installations pivot table
        $this->user->tenant()->attach($this->tenant->id);

        ProfileData::create([
            'user_id' => $this->user->id,
            'name' => 'Test',
            'surname' => 'User',
        ]);

        $this->user->assignRole(Roles::TENANT_ADMIN->value);

        // Create comprehensive metadata including invoice configuration
        $metadata = [
            'accounting' => [
                'regon' => '*********',
                'bdo' => 'TEST123',
            ],
            'bank_accounts' => [
                [
                    'account_name' => 'Konto główne',
                    'bank_name' => 'Test Bank',
                    'bank_account' => '12 3456 7890 1234 5678 9012 3456',
                    'bank_swift' => 'TESTPL22',
                    'bank_iban' => null,
                    'bank_currency' => 'PLN',
                ]
            ],
            'images' => [],
            'invoice_configuration' => [
                'selected_template' => 'v2',
                'special_note' => 'Test special note',
                'templates' => [
                    'v2' => [
                        'selected_fields' => ['field1', 'field2', 'field3'],
                        'updated_at' => now()->toISOString(),
                    ]
                ]
            ]
        ];

        TenantMeta::create([
            'tenant_id' => $this->tenant->id,
            'meta' => $metadata,
        ]);

        $this->actingAs($this->user);
    }

    public function test_invoice_configuration_is_preserved_during_profile_update()
    {
        // Get initial invoice configuration
        $initialInvoiceConfig = $this->tenant->fresh()->getInvoiceConfiguration();

        $this->assertEquals('v2', $initialInvoiceConfig['selected_template']);
        $this->assertEquals('Test special note', $initialInvoiceConfig['special_note']);
        $this->assertEquals(['field1', 'field2', 'field3'], $initialInvoiceConfig['templates']['v2']['selected_fields']);

        // Update only basic company data (not metadata) to test preservation
        $component = Livewire::test(CompanyProfile::class)
            ->set('data.name', 'Updated Company Name')
            ->set('data.city', 'Updated City')
            ->set('data.phone', '*********')
            ->call('save');

        // Verify the save was successful
        $component->assertHasNoErrors();

        // Refresh tenant and check that basic data was updated
        $updatedTenant = $this->tenant->fresh();
        $this->assertEquals('Updated Company Name', $updatedTenant->name);
        $this->assertEquals('Updated City', $updatedTenant->city);
        $this->assertEquals('*********', $updatedTenant->phone);

        // Verify that invoice configuration was preserved
        $preservedInvoiceConfig = $updatedTenant->getInvoiceConfiguration();

        $this->assertEquals('v2', $preservedInvoiceConfig['selected_template']);
        $this->assertEquals('Test special note', $preservedInvoiceConfig['special_note']);
        $this->assertEquals(['field1', 'field2', 'field3'], $preservedInvoiceConfig['templates']['v2']['selected_fields']);

        // Verify that original metadata was preserved
        $metaDto = DTOTenantMetadata::make($updatedTenant->meta->meta);
        $this->assertEquals('*********', $metaDto->getAccounting()->regon); // Should be unchanged
        $this->assertEquals('TEST123', $metaDto->getAccounting()->bdo); // Should be unchanged

        // Verify that bank account was preserved
        $bankAccounts = $metaDto->getBankAccounts();
        $this->assertEquals('Konto główne', $bankAccounts->first()->account_name); // Should be unchanged
    }

    public function test_metadata_updates_preserve_invoice_configuration()
    {
        // Test that updating metadata fields preserves invoice configuration
        $component = Livewire::test(CompanyProfile::class);

        // Update accounting data
        $component
            ->set('data.meta.accounting.regon', '*********')
            ->set('data.meta.accounting.bdo', 'NEWBDO123')
            ->call('save');

        // Verify invoice configuration is still intact
        $updatedTenant = $this->tenant->fresh();
        $invoiceConfig = $updatedTenant->getInvoiceConfiguration();

        $this->assertEquals('v2', $invoiceConfig['selected_template']);
        $this->assertEquals('Test special note', $invoiceConfig['special_note']);
        $this->assertEquals(['field1', 'field2', 'field3'], $invoiceConfig['templates']['v2']['selected_fields']);

        // Verify that accounting data was updated
        $metaDto = DTOTenantMetadata::make($updatedTenant->meta->meta);
        $this->assertEquals('*********', $metaDto->getAccounting()->regon);
        $this->assertEquals('NEWBDO123', $metaDto->getAccounting()->bdo);
    }

    public function test_multiple_profile_updates_preserve_invoice_configuration()
    {
        // Perform multiple updates to ensure data preservation is consistent
        $component = Livewire::test(CompanyProfile::class);

        // First update - basic data
        $component
            ->set('data.name', 'First Update')
            ->set('data.email', '<EMAIL>')
            ->call('save');

        // Second update - more basic data
        $component
            ->set('data.name', 'Second Update')
            ->set('data.postcode', '12-345')
            ->call('save');

        // Third update - accounting data only
        $component
            ->set('data.meta.accounting.bdo', 'NEWBDO123')
            ->call('save');

        // Verify invoice configuration is still intact after multiple updates
        $finalTenant = $this->tenant->fresh();
        $finalInvoiceConfig = $finalTenant->getInvoiceConfiguration();

        $this->assertEquals('v2', $finalInvoiceConfig['selected_template']);
        $this->assertEquals('Test special note', $finalInvoiceConfig['special_note']);
        $this->assertEquals(['field1', 'field2', 'field3'], $finalInvoiceConfig['templates']['v2']['selected_fields']);

        // Verify that the latest updates were applied
        $this->assertEquals('Second Update', $finalTenant->name);
        $this->assertEquals('12-345', $finalTenant->postcode);

        $metaDto = DTOTenantMetadata::make($finalTenant->meta->meta);
        $this->assertEquals('NEWBDO123', $metaDto->getAccounting()->bdo);
        $this->assertEquals('*********', $metaDto->getAccounting()->regon); // Should be unchanged
    }

    public function test_empty_invoice_configuration_is_handled_correctly()
    {
        // Create tenant with no invoice configuration
        $tenantWithoutConfig = Tenant::factory()->create();
        $userWithoutConfig = User::factory()->create();
        $userWithoutConfig->tenant()->attach($tenantWithoutConfig->id);
        $userWithoutConfig->assignRole(Roles::TENANT_ADMIN->value);

        TenantMeta::create([
            'tenant_id' => $tenantWithoutConfig->id,
            'meta' => [
                'accounting' => ['regon' => '*********', 'bdo' => 'TEST'],
                'bank_accounts' => [],
                'images' => []
                // No invoice_configuration
            ],
        ]);

        $this->actingAs($userWithoutConfig);

        // Update profile
        $component = Livewire::test(CompanyProfile::class)
            ->set('data.name', 'Company Without Config')
            ->call('save');

        $component->assertHasNoErrors();

        // Verify that empty invoice configuration doesn't cause issues
        $updatedTenant = $tenantWithoutConfig->fresh();
        $this->assertEquals('Company Without Config', $updatedTenant->name);

        $invoiceConfig = $updatedTenant->getInvoiceConfiguration();
        $this->assertArrayHasKey('selected_template', $invoiceConfig->toArray());
    }

    public function test_demonstrates_bug_fix_scenario()
    {
        // This test demonstrates the exact scenario that was causing data loss
        // Before the fix: invoice configuration would be lost after profile update
        // After the fix: invoice configuration should be preserved

        // Set up a tenant with invoice configuration (simulating InvoiceConfiguration page usage)
        $invoiceConfig = [
            'selected_template' => 'custom_template',
            'special_note' => 'Important invoice note',
            'templates' => [
                'custom_template' => [
                    'selected_fields' => ['custom_field1', 'custom_field2'],
                    'updated_at' => now()->toISOString(),
                ]
            ]
        ];

        // Update the tenant metadata to include invoice configuration
        $existingMeta = $this->tenant->meta->meta;
        $existingMeta['invoice_configuration'] = $invoiceConfig;
        $this->tenant->meta->update(['meta' => $existingMeta]);

        // User updates company profile (the action that was causing data loss)
        $component = Livewire::test(CompanyProfile::class)
            ->set('data.name', 'Updated Company After Invoice Config')
            ->set('data.address', 'New Address 123')
            ->call('save');

        $component->assertHasNoErrors();

        // Verify that invoice configuration is still intact (this would fail before the fix)
        $updatedTenant = $this->tenant->fresh();
        $preservedConfig = $updatedTenant->getInvoiceConfiguration();

        $this->assertEquals('custom_template', $preservedConfig['selected_template']);
        $this->assertEquals('Important invoice note', $preservedConfig['special_note']);
        $this->assertEquals(['custom_field1', 'custom_field2'], $preservedConfig['templates']['custom_template']['selected_fields']);

        // Verify that the profile update still worked
        $this->assertEquals('Updated Company After Invoice Config', $updatedTenant->name);
        $this->assertEquals('New Address 123', $updatedTenant->address);
    }
}
