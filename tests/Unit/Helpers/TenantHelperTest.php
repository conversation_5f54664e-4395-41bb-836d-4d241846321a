<?php

namespace Tests\Unit\Helpers;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class TenantHelperTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush(); // Clear cache before each test
    }

    public function test_tenant_helper_returns_null_when_no_user_authenticated(): void
    {
        Auth::logout();
        
        $result = tenant();
        
        $this->assertNull($result);
    }

    public function test_tenant_helper_caches_tenant_data(): void
    {
        // Create a user and tenant
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $user->tenant()->attach($tenant->id);
        
        Auth::login($user);
        
        // First call should hit the database and cache the result
        $result1 = tenant();
        
        $this->assertInstanceOf(Tenant::class, $result1);
        $this->assertEquals($tenant->id, $result1->id);
        
        // Verify the data is cached
        $cacheKey = 'tenant.user_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
        
        // Second call should return cached data
        $result2 = tenant();
        $this->assertEquals($result1->id, $result2->id);
    }

    public function test_tenant_helper_respects_refresh_parameter(): void
    {
        // Create a user and tenant
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $user->tenant()->attach($tenant->id);
        
        Auth::login($user);
        
        // First call to populate cache
        $result1 = tenant();
        $this->assertInstanceOf(Tenant::class, $result1);
        
        // Call with refresh=true should bypass cache
        $result2 = tenant(true);
        $this->assertInstanceOf(Tenant::class, $result2);
        $this->assertEquals($result1->id, $result2->id);
    }

    public function test_tenant_helper_returns_null_when_user_has_no_tenant(): void
    {
        // Create a user without tenant
        $user = User::factory()->create();
        Auth::login($user);
        
        $result = tenant();
        
        $this->assertNull($result);
    }

    public function test_tenant_flush_clears_cache(): void
    {
        // Create a user and tenant
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $user->tenant()->attach($tenant->id);
        
        Auth::login($user);
        
        // Populate cache
        $result = tenant();
        $this->assertInstanceOf(Tenant::class, $result);
        
        $cacheKey = 'tenant.user_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
        
        // Flush cache
        tenantFlush();
        
        // Verify cache is cleared
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_tenant_flush_does_nothing_when_no_user_authenticated(): void
    {
        Auth::logout();
        
        // This should not throw any errors
        tenantFlush();
        
        $this->assertTrue(true); // Test passes if no exception is thrown
    }

    public function test_tenant_helper_uses_correct_cache_ttl(): void
    {
        // Create a user and tenant
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $user->tenant()->attach($tenant->id);
        
        Auth::login($user);
        
        // Call tenant helper to populate cache
        tenant();
        
        $cacheKey = 'tenant.user_' . $user->id;
        
        // Verify cache exists
        $this->assertTrue(Cache::has($cacheKey));
        
        // The TTL should be 24 hours (86400 seconds)
        // We can't easily test the exact TTL, but we can verify the data is cached
        $cachedData = Cache::get($cacheKey);
        $this->assertInstanceOf(Tenant::class, $cachedData);
        $this->assertEquals($tenant->id, $cachedData->id);
    }

    public function test_tenant_helper_cache_key_format(): void
    {
        // Create a user and tenant
        $user = User::factory()->create();
        $tenant = Tenant::factory()->create();
        $user->tenant()->attach($tenant->id);
        
        Auth::login($user);
        
        // Call tenant helper
        tenant();
        
        // Verify the cache key format
        $expectedCacheKey = 'tenant.user_' . $user->id;
        $this->assertTrue(Cache::has($expectedCacheKey));
        
        // Verify other potential key formats don't exist
        $oldSessionKey = $user->id . '_tenant';
        $this->assertFalse(Cache::has($oldSessionKey));
    }
}
