<?php
    $issuer = $tradeDoc->getMeta()->issuer_address ?? [];
    $buyer = $tradeDoc->buyer;
    $daysUntilDue = $tradeDoc->getDaysUntilDue();
    $daysText = $daysUntilDue === 1 ? 'jutro' : "za {$daysUntilDue} dni";
?>
@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Przypomnienie o płatności'])
@endsection

@section('content')
    <p>Szanowny Kliencie,</p>

    <p>Uprzejmie przypominamy o zbliżającym się terminie płatności faktury <strong>{{ $tradeDoc->full_doc_number }}</strong>.</p>

    @component('email.components.panel')
        <p><strong>Szczegóły faktury:</strong></p>
        <ul>
            <li><strong>Numer faktury:</strong> {{ $tradeDoc->full_doc_number }}</li>
            <li><strong>Data wystawienia:</strong> {{ $tradeDoc->issued_at->format('d.m.Y') }}</li>
            <li><strong>Data sprzedaży:</strong> {{ $tradeDoc->sells_date->format('d.m.Y') }}</li>
            <li><strong>Termin płatności:</strong> {{ $tradeDoc->payment_due_date->format('d.m.Y') }} ({{ $daysText }})</li>
            <li><strong>Kwota do zapłaty:</strong> {{ number_format($tradeDoc->gross, 2, ',', ' ') }} {{ $tradeDoc->currency }}</li>
            @if($tradeDoc->payment_type->label())
                <li><strong>Sposób płatności:</strong> {{ $tradeDoc->payment_type->label() }}</li>
            @endif
        </ul>
    @endcomponent

    @if($buyer)
        @component('email.components.panel')
            <p><strong>Nabywca:</strong></p>
            <p>{{ $buyer->name }}<br>
            @if($buyer->address)
                {{ $buyer->address }}<br>
            @endif
            @if($buyer->postcode || $buyer->city)
                {{ $buyer->postcode }} {{ $buyer->city }}<br>
            @endif
            @if($buyer->vat_id)
                NIP: {{ $buyer->vat_id }}
            @endif
            </p>
        @endcomponent
    @endif

    @if(count($issuer) > 0)
        @component('email.components.panel')
            <p><strong>Dane do przelewu:</strong></p>
            <p><strong>Odbiorca:</strong> {{ $issuer['name'] }}<br>
            @if($issuer['address'] ?? false)
                {{ $issuer['address'] }}<br>
            @endif
            @if(($issuer['postcode'] ?? false) || ($issuer['city'] ?? false))
                {{ $issuer['postcode'] ?? '' }} {{ $issuer['city'] ?? '' }}<br>
            @endif
            @if($issuer['vat_id'] ?? false)
                NIP: {{ $issuer['vat_id'] }}<br>
            @endif
            </p>

            @if($tradeDoc->getMeta()->getBankAccount()->bank_account !== '')
                <p><strong>Numer konta:</strong> {{ $tradeDoc->getMeta()->getBankAccount()->bank_account }}</p>
            @endif

            <p><strong>Tytuł przelewu:</strong> Faktura {{ $tradeDoc->full_doc_number }}</p>
        @endcomponent
    @endif

    <p>Prosimy o dokonanie płatności w terminie. W przypadku pytań lub problemów z płatnością prosimy o kontakt.</p>

    @include('email.components.subcopy', ['content' => new \Illuminate\Support\HtmlString(
        "<p>Ta wiadomość została wysłana automatycznie z systemu " . config('app.name') . "</p>" .
        "<p>Proszę na nią nie odpowiadać.</p>"
    )])

    <p>Pozdrawiamy,<br>
    @if(count($issuer) > 0)
        {{ $issuer['name'] }}<br>
        @if($issuer['phone'] ?? false)
            Tel: {{ $issuer['phone'] }}<br>
        @endif
        @if($issuer['email'] ?? false)
            Email: {{ $issuer['email'] }}
        @endif
    @endif
    </p>
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
