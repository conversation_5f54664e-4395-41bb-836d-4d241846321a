<?php
    $issuer = $tradeDoc->getMeta()->issuer_address ?? [];
?>
@extends('email.layouts.main')

@section('header')
    @include('email.partials.header')
@endsection

@section('title')
    @include('email.partials.title', ['title' => 'Dokument handlowy'])
@endsection

@section('content')
    <p>Szanowny Kliencie,</p>

    <p>W załączniku przesyłamy dokument handlowy zgodnie z Państwa zamówieniem.</p>

    @component('email.components.panel')
        <p><strong>Szczegóły dokumentu:</strong></p>
        <ul>
            <li><strong>Numer dokumentu:</strong> {{ $tradeDoc->full_doc_number }}</li>
            <li><strong>Typ dokumentu:</strong> {{ $tradeDoc->type->label() }}</li>
            <li><strong>Data wystawienia:</strong> {{ $tradeDoc->issued_at->format('d.m.Y') }}</li>
            <li><strong>Data sprzedaży:</strong> {{ $tradeDoc->sells_date->format('d.m.Y') }}</li>
            <li><strong>Kwota brutto:</strong> {{ number_format($tradeDoc->gross, 2, ',', ' ') }} {{ $tradeDoc->currency }}</li>
            @if($tradeDoc->payment_due_date)
                <li><strong>Termin płatności:</strong> {{ $tradeDoc->payment_due_date->format('d.m.Y') }}</li>
            @endif
            <li><strong>Nabywca (NIP):</strong> {{ $tradeDoc->buyer->vat_id ?? 'brak' }}</li>
        </ul>
    @endcomponent>

    @if(count($issuer) > 0)
        @component('email.components.panel')
            <p><strong>Sprzedawca:</strong></p>
            <p>{{ $issuer['name'] }}<br>
            @if($issuer['address'] ?? false)
                {{ $issuer['address'] }}<br>
            @endif
            @if( ($issuer['postcode'] ?? false) || ($issuer['city'] ?? false))
                {{ $issuer['postcode'] ?? '' }} {{ $issuer['city'] ?? '' }}<br>
            @endif
            @if($issuer['vat_id'] ?? false)
                NIP: {{ $issuer['vat_id'] }}
            @endif
            </p>
        @endcomponent>
    @endif

    <p>Dokument został załączony do tej wiadomości w formacie PDF.</p>
    <p>W przypadku pytań prosimy o kontakt.</p>

    @include('email.components.subcopy', ['content' => new \Illuminate\Support\HtmlString(
    "<p>Ta wiadomość została wysłana automatycznie z systemu " . config('app.name') . "</p>" .
    "<p>Proszę na nią nie ODPOWIADAĆ!.</p>"
    )])

    <p>Pozdrawiamy,<br>
    @if(count($issuer) > 0)
        {{ $issuer['name'] }}<br>
    @endif
@endsection

@section('footer')
    @include('email.partials.footer')
@endsection
