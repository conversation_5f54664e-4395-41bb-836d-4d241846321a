<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $document->title }} - TwojeFaktury.eu</title>
    <style>
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .document-title {
            font-size: 20px;
            color: #666;
            margin-bottom: 5px;
        }

        .document-meta {
            font-size: 12px;
            color: #888;
        }

        .content {
            margin: 30px 0;
        }

        .content h1 {
            font-size: 18px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .content h2 {
            font-size: 16px;
            color: #444;
            margin-top: 25px;
            margin-bottom: 10px;
        }

        .content h3 {
            font-size: 14px;
            color: #555;
            margin-top: 20px;
            margin-bottom: 8px;
        }

        .content p {
            margin-bottom: 12px;
            text-align: justify;
        }

        .content ul, .content ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }

        .content li {
            margin-bottom: 5px;
        }

        .footer {
            border-top: 1px solid #ddd;
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        .version-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-size: 12px;
        }

        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .header {
                page-break-after: avoid;
            }

            .content h1, .content h2, .content h3 {
                page-break-after: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">TwojeFaktury.eu</div>
        <div class="document-title">{{ $document->title }}</div>
        <div class="document-meta">
            Wersja {{ $document->version_number }} |
            @if($document->publication_date)
                Opublikowany: {{ $document->publication_date->format('Y-m-d') }}
            @else
                Wersja robocza
            @endif
        </div>
    </div>

    <div class="version-info">
        <strong>Informacje o dokumencie:</strong><br>
        Rodzaj: {{ $document->document_type->label() }}<br>
        Wersja: {{ $document->version_number }}<br>
        Status: {{ $document->status->label() }}<br>
        @if($document->publication_date)
            Data publikacji: {{ $document->publication_date->timezone('Europe/Warsaw')->format('Y-m-d, H:i') }}<br>
        @else
            Ostatnia aktualizacja: {{ $document->updated_at->timezone('Europe/Warsaw')->format('Y-m-d, H:i') }}
        @endif
    </div>

    <div class="content">
        @yield('content')
    </div>

    <div class="footer">
        @if(blank($document->publication_date))
            <p>Dokument został wygenerowany {{ now()->timezone('Europe/Warsaw')->format('Y-m-d, H:i') }}</p>
        @endif
        <p> TwojeFaktury.eu - All rights reserved</p>
    </div>
</body>
</html>
