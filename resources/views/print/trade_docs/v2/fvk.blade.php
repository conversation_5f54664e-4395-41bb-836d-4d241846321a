<?php
$issuer = $document->issuer_address;
$seller = $document->options['different_seller'] ? $document->seller_address : $document->issuer_address;
$buyer = $document->buyer_address;
$bank = $document->bank_data;
$vat = $document->vat;
$vat_source = $document->vat_source;
$vat_final = $document->vat_final;
$options = $document->options;
$items = $document->items;
$sourcedoc = $record->getSourceDoc();
/**
 * @var \App\Models\DTOTenantMetadata $tenantMeta
 */
$tenantMeta = $record->tenant->getMeta();
$invoiceConfig = $record->tenant->getInvoiceConfiguration();
$selected_fields = collect($invoiceConfig->get('templates')['v2']['selected_fields'] ?? []);
?>
@extends('print.trade_docs.main')
@section('content')
    <table cellpadding="0" cellspacing="0" class="header">
        <!-- <PERSON><PERSON><PERSON> c<PERSON> faktury -->
        <tr class="top-logo">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <h2>Faktura VAT korekta {{$record->full_doc_number}}
                                @if ($options['duplicate'] ?? false)
                                    <br><small style="font-size: 70%;">Duplikat, data
                                        duplikatu: {{$options['duplicate']}}</small>
                                @endif
                            </h2>
                            @if ($selected_fields->contains('special_note') && filled($invoiceConfig->get('special_note')))
                                <div style="margin-top: 2mm;">
                                    <div>
                                        <span class="alert">{{$invoiceConfig->get('special_note')}}</span><br>
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td style="text-align: right;">
                            Data wystawienia: {{$record->issued_at->format('Y-m-d')}}<br>
                            {{--                            Data sprzedaży: {{$record->sells_date->format('Y-m-d')}}<br>--}}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr class="top">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Sprzedawca:</strong><br>
                            {!! nl2br($seller['name']) !!}<br>
                            {{$seller['address']}}<br>
                            {{$seller['postcode']}} {{$seller['city']}}<br>
                            @if ($seller['vat_id'])
                                NIP: {{$seller['vat_id']}}
                            @endif
                            @if($selected_fields->contains('bdo_register') && $tenantMeta->accounting->bdo ?? false)
                                <br>BDO: {{$tenantMeta->accounting->bdo}}
                            @endif
                        </td>
                        <td style="text-align: right;">
                            Termin płatności: {{$record->payment_due_date->format('Y-m-d')}}<br>
                            Forma płatności: {{$record->payment_type->label()}}<br>
                            Waluta: {{$record->currency}}<br>
                            Bank: {{$bank['bank_name'] ?? 'brak'}}<br>
                            Konto: @if($selected_fields->contains('iban_code') && $bank['bank_iban'] ?? false)
                                {{$bank['bank_iban']}}
                            @endif {{$bank['bank_account'] ?? 'brak'}}
                            @if($selected_fields->contains('swift_code') && $bank['bank_swift'] ?? false)
                                <br>SWIFT: {{$bank['bank_swift']}}
                            @endif
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Informacje o nabywcy -->
        <tr class="information">
            <td colspan="4">
                <table>
                    <tr>
                        <td>
                            <strong>Nabywca:</strong><br>
                            {!! nl2br($buyer['name']) !!}<br>
                            {{$buyer['address']}}<br>
                            {{$buyer['postcode']}} {{$buyer['city']}}<br>
                            @if ($buyer['vat_id'])
                                NIP: {{$buyer['vat_id']}} <br>
                            @endif
                        </td>
                        <td style="text-align: right;">
                            <strong>Dokument korygowany</strong><br>
                            Nr: <strong>{{$sourcedoc->full_doc_number}}</strong><br>
                            Data wystawienia: {{$sourcedoc->issued_at->format('Y-m-d')}}<br>
                            Data sprzedaży: {{$sourcedoc->sells_date->format('Y-m-d')}}<br>
                            Data płatności: {{$sourcedoc->payment_due_date->format('Y-m-d')}}<br>
                            Waluta: {{$sourcedoc->currency}}<br>
                            {{--                            @if($document->getOption('reverse_charge', false))--}}
                            {{--                                <strong>{{__('app.trade_docs._.reverse_charge')}}</strong><br>--}}
                            {{--                            @endif--}}
                            {{--                            @if($document->getOption('mpp', false))--}}
                            {{--                                <strong>{{__('app.trade_docs._.mpp')}}</strong><br>--}}
                            {{--                            @endif--}}
                            <strong>VAT </strong>{{$record->vat_method->label()}} <br>

                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    @include('print.trade_docs.v2.parts.fvk_items')
    @include('print.trade_docs.v2.parts.fvk_summary')
    @include('print.trade_docs.v2.parts.footer')
@endsection
