# Trade Document Email Feature

This document describes the new email functionality for sending trade documents (PDF invoices) via email.

## Overview

The feature allows users to send trade documents as PDF attachments via email from two locations:
1. **ListTradeDocs table**: Row action button for each trade document
2. **AddTradeDocItem page**: Header action button

## Features

### Email Modal
When the email action is clicked, a modal popup displays with:
- **Recipient email field**: Pre-populated with the buyer's email from the Partner model
- **"Send to me" checkbox**: When checked, also sends a copy to the current user's email
- **Manual email editing**: Users can modify the pre-populated email address
- **Email validation**: Ensures valid email format

### Email Template
- Professional layout using existing email components
- Includes trade document details (number, type, date, amount)
- PDF invoice attached automatically
- Consistent styling with current application emails
- Supports both Polish and multi-language content

### Technical Implementation
- Uses Filament's action system for buttons
- Implements proper modal forms with validation
- Leverages existing PDF generation functionality
- Follows <PERSON><PERSON>'s mailing conventions with queued emails
- Maintains consistency with existing Filament 3 patterns

## Files Created/Modified

### New Files
1. `app/Mail/TradeDocumentEmail.php` - Mailable class for trade document emails
2. `resources/views/email/trade-document.blade.php` - Email template
3. `app/Filament/Actions/SendTradeDocumentEmailAction.php` - Reusable action class

### Modified Files
1. `app/Filament/App/Resources/TradeDocResource/Pages/ListTradeDocs.php` - Added email row action
2. `app/Filament/App/Resources/TradeDocResource/Pages/AddTradeDocItem.php` - Added email header action

## Usage

### From Trade Documents List
1. Navigate to the Trade Documents list
2. Click the envelope icon (📧) in the row actions for any document with items
3. The modal will open with the buyer's email pre-filled
4. Optionally check "Send to me" to receive a copy
5. Click "Send" to send the email

### From Trade Document Details
1. Open a trade document in the AddTradeDocItem page
2. Click the "Send Email" button in the header actions (visible only for documents with items)
3. Follow the same process as above

## Email Content

The email includes:
- Professional greeting
- Document details (number, type, dates, amount)
- Buyer information
- PDF attachment of the invoice
- Professional closing with company information

## Technical Details

### Dependencies
- Uses existing `TradeDocsRepository::getPDFData()` for PDF generation
- Leverages `TenantRepository::getTenantTradeDocVariant()` for template selection
- Integrates with Laravel's Mail system and queue system

### Error Handling
- Graceful handling of PDF generation failures
- Email validation before sending
- User notifications for success/failure
- Logging of errors for debugging

### Security
- Email validation to prevent invalid addresses
- User authentication required
- Tenant isolation maintained
- PDF generation uses existing security measures

## Configuration

### Queue Configuration
Emails are sent via the 'emails' queue. Ensure your queue worker is running:
```bash
./server artisan queue:work
```

### Email Configuration
Ensure your Laravel mail configuration is properly set up in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## Testing

The feature can be tested by:
1. Creating a trade document with items
2. Ensuring the buyer partner has a valid email address
3. Using the email action from either location
4. Checking that emails are queued and sent properly

## Future Enhancements

Potential improvements could include:
- Email templates in multiple languages
- Custom email message field
- Email delivery status tracking
- Bulk email sending for multiple documents
- Email scheduling functionality
