<?php

namespace App\Providers;

use App\Events\AccountDeleted;
use App\Events\PaymentCanceled;
use App\Events\PaymentCompleted;
use App\Events\Registration\RegistrationConfirmed;
use App\Events\Registration\RegistrationCreated;
use App\Events\Registration\RegistrationFinished;
use App\Events\SubscriptionCanceled;
use App\Listeners\AccountDeletedListener;
use App\Listeners\JobTaskSubscriber;
use App\Listeners\PaymentCanceledListener;
use App\Listeners\PaymentCompletedListener;
use App\Listeners\Registration\RegistrationConfirmedListener;
use App\Listeners\Registration\RegistrationCreatedListener;
use App\Listeners\Registration\RegistrationFinishedListener;
use App\Listeners\SubscriptionCanceledListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        PaymentCompleted::class => [
            PaymentCompletedListener::class,
        ],
        PaymentCanceled::class => [
            PaymentCanceledListener::class,
        ],
        SubscriptionCanceled::class => [
            SubscriptionCanceledListener::class,
        ],
        RegistrationCreated::class => [
            RegistrationCreatedListener::class,
        ],
        RegistrationConfirmed::class => [
            RegistrationConfirmedListener::class,
        ],
        RegistrationFinished::class => [
            RegistrationFinishedListener::class,
        ],
        AccountDeleted::class => [
            AccountDeletedListener::class,
        ],
    ];

    protected $subscribe = [
        JobTaskSubscriber::class
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
