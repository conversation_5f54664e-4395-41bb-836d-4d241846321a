<?php

namespace App\Providers;

use App\Repositories\DrugsRepository;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use <PERSON>vel\Nightwatch\Facades\Nightwatch;
use <PERSON>vel\Nightwatch\Records\Query;

class DrugImportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind('DrugImport', function ($app) {
            return new DrugsRepository($this->app->make(Http::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->registerNightwatchExceptions();
    }

    private function registerNightwatchExceptions(): void
    {
        Nightwatch::rejectQueries(static fn (Query $query) => str_contains($query->sql, 'into `drug_source`'));
    }
}
