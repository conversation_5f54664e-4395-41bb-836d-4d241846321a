<?php

namespace App\Console\Commands;

use App\Jobs\AggregateStatsJob;
use App\Services\StatsService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AggregateStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'stats:aggregate
                            {date? : Date to aggregate (Y-m-d format, defaults to yesterday)}
                            {--queue : Dispatch as a queue job instead of running synchronously}
                            {--no-clear : Do not clear Redis keys after aggregation}
                            {--show-only : Show stats for date and exit}
                            {--force : Force aggregation even if already exists}';

    /**
     * The console command description.
     */
    protected $description = 'Aggregate daily statistics from Redis to database';

    /**
     * Execute the console command.
     */
    public function handle(StatsService $statsService): int
    {
        try {
            $dateString = $this->argument('date');

            if ($dateString) {
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $dateString);
                } catch (\Exception $e) {
                    $this->error('Invalid date format. Please use Y-m-d format.');
                    return self::FAILURE;
                }
            } else {
                $date = Carbon::yesterday();
            }

            if ($this->option('show-only')) {
                $this->info("Showing statistics for {$date->format('Y-m-d')}");

                $redisStats = $statsService->getDailyStats($date);
                $redisCount = count($redisStats);

                if ($redisCount === 0) {
                    $this->warn("No Redis statistics found for {$date->format('Y-m-d')}");
                    return self::SUCCESS;
                }

                $this->table(
                    ['Event Name', 'Count'],
                    array_map(function ($eventName, $count) {
                        return [$eventName, $count];
                    }, array_keys($redisStats), array_values($redisStats))
                );

                return self::SUCCESS;
            }

            $this->info("Processing statistics aggregation for {$date->format('Y-m-d')}");

            // Check if we should use queue
            if ($this->option('queue')) {
                $clearRedis = !$this->option('no-clear');
                AggregateStatsJob::dispatch($date, $clearRedis);
                $this->info('Aggregation job dispatched to queue.');
                return self::SUCCESS;
            }

            // Check if aggregation already exists (unless forced)
            if (!$this->option('force')) {
                $existingStats = \App\Models\DailyStatistic::where('date', $date->format('Y-m-d'))->count();
                if ($existingStats > 0) {
                    if (!$this->confirm("Statistics for {$date->format('Y-m-d')} already exist ({$existingStats} records). Continue anyway?")) {
                        $this->info('Aggregation cancelled.');
                        return self::SUCCESS;
                    }
                }
            }

            // Get current Redis stats before aggregation
            $redisStats = $statsService->getDailyStats($date);
            $redisCount = count($redisStats);

            if ($redisCount === 0) {
                $this->warn("No Redis statistics found for {$date->format('Y-m-d')}");
                return self::SUCCESS;
            }

            $this->info("Found {$redisCount} statistics in Redis for {$date->format('Y-m-d')}");

            // Show progress bar for aggregation
            $this->withProgressBar($redisStats, function ($count, $eventName) use ($statsService, $date) {
                // The actual aggregation happens in the service method
            });

            $this->newLine();

            // Perform aggregation
            $aggregatedCount = $statsService->aggregateDailyStats($date);
            $this->info("Successfully aggregated {$aggregatedCount} statistics to database.");

            // Clear Redis if not disabled
            if (!$this->option('no-clear')) {
                $clearedCount = $statsService->clearDailyRedisStats($date);
                $this->info("Cleared {$clearedCount} Redis keys.");
            }

            // Show summary
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Date', $date->format('Y-m-d')],
                    ['Redis Stats Found', $redisCount],
                    ['Database Records Created/Updated', $aggregatedCount],
                    ['Redis Keys Cleared', $this->option('no-clear') ? 'Skipped' : $clearedCount ?? 0],
                ]
            );

            Log::info("[Stats] Command aggregation completed for {$date->format('Y-m-d')}: {$aggregatedCount} records");

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Failed to aggregate statistics: " . $e->getMessage());
            Log::error("[Stats] Command aggregation failed: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}
