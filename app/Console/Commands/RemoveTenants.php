<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Repositories\TenantRepository;
use App\Services\TenantArchiverService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RemoveTenants extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-tenants
                        {--encrypt= \'true\' or \'false\' to force encryption}
                        {--archiveOnly : Only archive tenants, don\'t remove them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantRepository = new TenantRepository();
        $archiveService = new TenantArchiverService();

        if ($this->option('archiveOnly')) {
            $this->info('Only archiving tenants, not removing them');
        }

        if ($this->option('encrypt') === 'true') {
            $archiveService->encrypted();
        }

        if ($this->option('encrypt') === 'false') {
            $archiveService->encrypted(false);
        }

        $tenants = Tenant::query()->markedForDelete()->get();

        if ($tenants->count() === 0) {
            $this->info('No tenants to remove');
            return;
        }

        foreach ($tenants as $tenant) {
            $archiveSuccess = $archiveService->tenant($tenant)
                ->compressed(compress: true, removeSourceFiles: true)
                ->archiveTenantData();

            if (!$archiveSuccess) {
                Log::error('Cannot delete tenant ' . $tenant->name . ' - archive failed');
                $this->info('Tenant ' . $tenant->name . ' not archived and removed');
                continue;
            }

            if ($this->option('archiveOnly')) {
                $this->info('Tenant ' . $tenant->name . ' archived');
                continue;
            }

            try {
                $tenantRepository->removeTenant($tenant, true);
                $this->info('Tenant ' . $tenant->name . ' archived and removed');
            } catch (\Exception $e) {
                Log::error('Cannot delete tenant ' . $tenant->name . ' - ' . $e->getMessage());
                $this->info('Tenant ' . $tenant->name . ' not removed');
            }
        }
    }
}
