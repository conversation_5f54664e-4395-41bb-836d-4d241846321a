<?php

namespace App\Console\Commands;

use App\Models\Payment;
use App\Models\PurchaseDoc;
use App\Models\Tenant;
use App\Repositories\TenantRepository;
use App\Repositories\TradeDocsRepository;
use App\Services\TenantArchiverService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class TestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $tas = new TenantArchiverService();
        $tas->tenant(Tenant::find(667))
            ->encrypted()
            ->compressed(removeSourceFiles: true)
            ->archiveTenantData();
        $this->info('Done. Hash ' . $tas->getTenant()->hash);
        exit();
        $payment = Payment::find(85);

        Mail::to('<EMAIL>')->send(new \App\Mail\PaymentSuccess($payment));
        exit;

        $t = Tenant::find(2);
        $tr = new TenantRepository();

        Storage::disk('local')->put(
            "/tenant_export/{$t->id}/tenant.json",
            $tr->getTenantForExport($t)->toJson(JSON_PRETTY_PRINT)
        );

        $td = $tr->getTenantTradeDocsForExport($t);
        foreach ($td as $year => $pc) {
            Storage::disk('local')->put(
                "/tenant_export/{$t->id}/tradedoc_{$year}.json",
                $pc->toJson(JSON_PRETTY_PRINT)
            );
        }

        $pc = $tr->getTenantPurchaseDocsForExport($t);
        foreach ($pc as $year => $item) {
            Storage::disk('local')->put(
                "/tenant_export/{$t->id}/purdoc_{$year}.json",
                $item->toJson(JSON_PRETTY_PRINT)
            );
        }


        $wc = $tr->getTenantWarehouseDocsForExport($t);
        foreach ($wc as $year => $item) {
            Storage::disk('local')->put(
                "/tenant_export/{$t->id}/whdoc_{$year}.json",
                $item->toJson(JSON_PRETTY_PRINT)
            );
        }

        dd();
        if (!Storage::disk('local')->exists('test.txt')) {
            Storage::disk('local')->put('test.txt', 1);
            exit(0);
        }
        $cs = Storage::disk('local')->get('test.txt');
        Storage::disk('local')->put('test.txt', $cs + 1);
        Mail::to('<EMAIL>')->queue((new \App\Mail\TestEmail())->setSubject('New value: ' . $cs + 1));
        exit(0);
    }
}
