<?php

namespace App\Console\Commands;

use App\Mail\PaymentReminderEmail;
use App\Models\TradeDoc;
use App\Repositories\TenantRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendPaymentReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-payment-reminders {--dry-run : Preview reminders without sending emails}
    {--tenant= : Process only specific tenant ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send payment reminder emails for upcoming invoice due dates';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $tenantId = $this->option('tenant');

        $this->info('Starting payment reminder process...');

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No emails will be sent');
        }

        // Get trade documents that need payment reminders
        $tradeDocs = $this->getTradeDocsForReminders($tenantId);

        $totalFound = $tradeDocs->count();
        $this->info("Found {$totalFound} invoice(s) requiring payment reminders");

        if ($totalFound === 0) {
            $this->info('No invoices found requiring payment reminders. Exiting.');
            return self::SUCCESS;
        }

        // Display preview information
        if ($isDryRun || $this->option('verbose')) {
            $this->displayReminderPreview($tradeDocs);
        }

        if ($isDryRun) {
            $this->info('Dry run completed. No emails were sent.');
            return self::SUCCESS;
        }

        // Send payment reminder emails
        return $this->sendPaymentReminders($tradeDocs);
    }

    /**
     * Get trade documents that need payment reminders
     */
    private function getTradeDocsForReminders(?string $tenantId)
    {
        $query = TradeDoc::with(['buyer', 'issuer'])
            ->where('payment_reminder_enabled', true)
            ->where('is_paid', false)
            ->whereNull('payment_reminder_sent_at')
            ->whereNotNull('payment_due_date')
            ->whereNotNull('payment_reminder_days');

        if ($tenantId) {
            $query->where('issuer_id', $tenantId);
        }

        return $query->get()->filter(function (TradeDoc $tradeDoc) {
            // Check if buyer has email
            if (!$tradeDoc->buyer || !$tradeDoc->buyer->email) {
                return false;
            }
            return $tradeDoc->shouldSendPaymentReminder();
        });
    }

    /**
     * Display preview of reminders that would be sent
     */
    private function displayReminderPreview($tradeDocs): void
    {
        $this->newLine();
        $this->info('Payment reminders to be sent:');

        $headers = ['Invoice', 'Buyer', 'Email', 'Due Date', 'Days Until Due', 'Reminder date', 'Amount', 'Tenant'];
        $rows = [];

        foreach ($tradeDocs as $tradeDoc) {
            $rows[] = [
                $tradeDoc->full_doc_number,
                $tradeDoc->buyer->name ?? 'N/A',
                $tradeDoc->buyer->email ?? 'N/A',
                $tradeDoc->payment_due_date->format('Y-m-d'),
                $tradeDoc->getDaysUntilDue(),
                $tradeDoc->getPaymentReminderDate()?->format('Y-m-d') ?? 'brak',
                number_format($tradeDoc->gross, 2) . ' ' . $tradeDoc->currency,
                $tradeDoc->issuer->name ?? 'N/A'
            ];
        }

        $this->table($headers, $rows);
        $this->newLine();
    }

    /**
     * Send payment reminder emails
     */
    private function sendPaymentReminders($tradeDocs): int
    {
        $successCount = 0;
        $failureCount = 0;
        $skippedCount = 0;

        $progressBar = $this->output->createProgressBar($tradeDocs->count());
        $progressBar->start();

        foreach ($tradeDocs as $tradeDoc) {
            try {
                if (!$tradeDoc->buyer || !$tradeDoc->buyer->email) {
                    $this->error("Invoice {$tradeDoc->full_doc_number}: No buyer or email found");
                    $failureCount++;
                    $progressBar->advance();
                    continue;
                }

                if (!$tradeDoc->issuer) {
                    $this->error("Invoice {$tradeDoc->full_doc_number}: No issuer found");
                    $failureCount++;
                    $progressBar->advance();
                    continue;
                }

                // Check tenant email limits
                if (!TenantRepository::canTenantSendEmails($tradeDoc->issuer)) {
                    $this->warn("Invoice {$tradeDoc->full_doc_number}: Tenant email limit reached, skipping");
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }

                // Increment tenant email limit
                if (!TenantRepository::incrementEmailSentLimit($tradeDoc->issuer)) {
                    $this->warn("Invoice {$tradeDoc->full_doc_number}: Could not increment email limit, skipping");
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }

                // Send email
                Mail::to($tradeDoc->buyer->email)
                    ->send(new PaymentReminderEmail($tradeDoc));

                // Mark as sent
                $tradeDoc->markPaymentReminderSent();

                Log::info("Payment reminder sent for invoice
                {$tradeDoc->full_doc_number} to {$tradeDoc->buyer->email}");
                $successCount++;
            } catch (\Exception $e) {
                $this->error("Failed to send reminder for invoice {$tradeDoc->full_doc_number}: " . $e->getMessage());
                Log::error("Payment reminder failed for invoice {$tradeDoc->full_doc_number}: " . $e->getMessage());
                $failureCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($successCount, $failureCount, $skippedCount);

        return $failureCount > 0 ? self::FAILURE : self::SUCCESS;
    }

    /**
     * Display command results
     */
    private function displayResults(int $successCount, int $failureCount, int $skippedCount): void
    {
        $this->info('Payment reminder process completed:');
        $this->info("✅ Successfully sent: {$successCount}");

        if ($skippedCount > 0) {
            $this->warn("⏭️  Skipped (email limits): {$skippedCount}");
        }

        if ($failureCount > 0) {
            $this->error("❌ Failed: {$failureCount}");
        }

        $this->newLine();
    }
}
