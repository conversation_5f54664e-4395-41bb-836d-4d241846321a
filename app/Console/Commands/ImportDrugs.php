<?php

namespace App\Console\Commands;

use App\Repositories\DrugsRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ImportDrugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:import-drugs
    {--storage= : Storage type. Can be \'file\' or \'db\' (default db)}
    {--fetchOnly : fetch and save XML file only}
    {--importFromJson : get data from previously processed json file. Storage = \'db\'}
    {--processXMLFile= : process xml file. Can\'t be used with \'fetchOnly\'}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import drugs list from https://rejestry.ezdrowie.gov.pl/registry/rpl';


    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $type = 'overall';

        $storage = match ($this->option('storage')) {
            default => 'db',
            'file' => 'file',
        };


        /**
         * @var DrugsRepository $repo
         */
        $repo = app(DrugsRepository::class);
        $repo->import_type = $type;
        $repo->storage = $storage;

        $this->info("Import drugs. Storage: {$storage}, method: {$type}");
        Log::notice("Import drugs. Storage: {$storage}, method: {$type}");

        if ($this->option('importFromJson')) {
            $this->importFromJson($repo);
        } elseif ($this->option('processXMLFile')) {
            $this->processXMLFile($repo, $this->option('processXMLFile'));
        } elseif ($this->option('fetchOnly')) {
            $this->fetchOnly($repo);
        } else {
            $this->standard($repo);
        }


        $this->info($repo->getInfoString());
        $this->info('Done');
        Log::info('Import drugs run');
    }

    protected function standard(DrugsRepository $repo): void
    {
        if (null === $file = $repo->fetchRemoteFile()) {
            $this->error($repo->error);
            Log::error($repo->error);
            exit();
        }
        $this->info('File fetched');

        try {
            $this->info('Processing file ' . $file);
            $repo->processXMLFile($file);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
            Log::error('Processing file error: ' . $e->getMessage());
            exit();
        }
        $this->info('List processed');
        $repo->putToStorage();
        $this->info('List put to storage');
        Log::notice("Import drugs. List processed: {$repo->import_type}");
        $repo->clearTempFiles();
    }

    protected function fetchOnly(DrugsRepository $repo): void
    {
        if (null === $file = $repo->fetchRemoteFile()) {
            $this->error($repo->error);
            exit();
        }
        $this->info('File fetched');
    }

    protected function processXMLFile(DrugsRepository $repo, $file): void
    {
        $file = $repo->getStorageFilePath() . $file;

        try {
            $repo->processXMLFile($file);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
            exit();
        }
        $this->info('List processed');
        $repo->putToStorage();
        $this->info('List put to storage');
    }

    protected function importFromJson(DrugsRepository $repo): void
    {
        $repo->storage = 'db';

        try {
            $repo->importFromJson();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
            exit();
        }

        $repo->putToStorage();
        $this->info('List put to storage');
        $repo->clearTempFiles();
    }
}
