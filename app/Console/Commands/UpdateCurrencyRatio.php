<?php

namespace App\Console\Commands;

use App\Repositories\CurrencyRatesExchangeRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateCurrencyRatio extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wh:update-currency-ratio {--date= : date format YYYY-MM-DD}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update currencies exchange ratio from NBP (PLN) and ECB (EUR)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        foreach (['PLN', 'EUR'] as $currency) {
//            CurrencyRatesExchangeRepository::updateExchangeRates($currency, Carbon::now());
//        }

        $date = Carbon::now();
        if ($this->option('date')) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $this->option('date'));
            } catch (\Exception $e) {
                $this->warn('Invalid date format');
                $this->info('Usage: php artisan wh:update-currency-ratio --date=YYYY-MM-DD');
                Log::error('Update Currency Ratio: Invalid date format: ' . $this->option('date'));
                exit(1);
            }
        }
        $this->info('Updating exchange rates for: ' . $date->format('Y-m-d'));
        Log::notice('Updating exchange rates for: ' . $date->format('Y-m-d'));

        foreach (['EUR', 'PLN'] as $currency) {
            CurrencyRatesExchangeRepository::updateExchangeRates($currency, $date);
        }
    }
}
