<?php

namespace App\Console\Commands;

use App\Jobs\TestJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class TriggerQueueTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:test {--jobs=} {--queue=default}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Triggers a queue test by generating and enqueuing a large number of jobs.';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $numJobs = $this->option('jobs') ?? 1000;
        $queueName = $this->option('queue') ?? 'default';

        try {
            Log::info("Starting queue test: Enqueuing {$numJobs} jobs on queue: {$queueName}");

            for ($i = 0; $i < $numJobs; $i++) {
                TestJob::dispatch("Queue test: Job {$i} jobs on queue: {$queueName}")->onQueue($queueName);
            }

            Log::info("Successfully enqueued {$numJobs} jobs to queue: {$queueName}.");
            $this->info('Successfully triggered queue test with ' . $numJobs . ' jobs.');
        } catch (\Exception $e) {
            Log::error("Error triggering queue test: " . $e->getMessage()); // Log the error for debugging.
            $this->error('Failed to trigger queue test: ' . $e->getMessage(), 500);  //Return an error response.
            return 1;
        }

        return 0;
    }
}
