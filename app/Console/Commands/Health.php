<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>\Nightwatch\Facades\Nightwatch;

class Health extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:health';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public function __construct()
    {
        Nightwatch::dontSample();
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Nightwatch::dontSample();
        $this->info('OK');
    }
}
