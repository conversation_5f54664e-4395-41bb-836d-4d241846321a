<?php

namespace App\Console\Commands;

use App\DTO\GUS\FizycznaDaneTDO;
use App\DTO\GUS\PrawneDaneDTO;
use App\Repositories\GUSRepository;
use <PERSON><PERSON><PERSON>\BulkReportTypes;
use <PERSON><PERSON><PERSON>\Exception\InvalidUserKeyException;
use Gus<PERSON>pi\Exception\NotFoundException;
use Gus<PERSON><PERSON>\GusApi;
use Gus<PERSON>pi\ReportTypes;
use Illuminate\Console\Command;

class GusImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:gus-import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $gus = new GusApi(getenv('GUS_USER_KEY'), getenv('GUS_ENV'));
//        $gus = new GUSRepository();

        try {
            $nipToCheck = '8971764744'; //change to valid nip value
            $gus->login();

            $gusReports = $gus->getByNip($nipToCheck);
//            $gusReports = $gus->podmiotByNIP($nipToCheck);

//            var_dump($gusReports);
//            exit();
//            var_dump($gus->getBulkReport(
//                new \DateTimeImmutable('2025-01-31'),
//                BulkReportTypes::REPORT_DELETED_LOCAL_UNITS
//            ));

            foreach ($gusReports as $gusReport) {
                //you can change report type to other one
//                $reportType = ReportTypes::REPORT_PERSON;
//                dd($gusReport->jsonSerialize());
//                echo $gusReport->getName();
//                $this->info('Address: ' . $gusReport->getStreet() . ' ' . $gusReport->getPropertyNumber() . '/' . $gusReport->getApartmentNumber());
//                $this->info('Kod: ' . $gusReport->getZipCode() . ' ' . $gusReport->getCity());
//                $this->info('Typ ' . $gusReport->getType());
//                $this->info('Nip ' . $gusReport->getNip());
//                $this->info('NipStatus ' . $gusReport->getNipStatus());
//                $this->info('Regon ' . $gusReport->getRegon());
//                $this->info('Regon14 ' . $gusReport->getRegon14());
//                $this->info('Silo ' . $gusReport->getSilo());
//                $this->info('Aktywność end date ' . $gusReport->getActivityEndDate());
//                var_dump($gusReport->jsonSerialize());
                $reportType = match (strtoupper($gusReport->getType())) {
                    'F' => match ((int)$gusReport->getSilo()) {
                        1 => ReportTypes::REPORT_PERSON_CEIDG,
                        2 => ReportTypes::REPORT_PERSON_AGRO,
                        3 => ReportTypes::REPORT_PERSON_OTHER,
                        4 => ReportTypes::REPORT_PERSON_DELETED_BEFORE_20141108
                    },
                    'P' => ReportTypes::REPORT_ORGANIZATION,
                    default => null
                };
                if (blank($reportType)) {
                    $this->info('Report type not supported');
                    continue;
                }
                $fullReport = $gus->getFullReport($gusReport, $reportType);
                $regon = $gus->getByRegon($gusReport->getRegon());
                var_dump($regon);
                $rep = new FizycznaDaneTDO($fullReport[0]);
                dd($rep);
//                $this->info('NIP: ' . $rep->getPrawNip());
//                $this->info('Nazwa: ' . $rep->getPrawNazwa());
//                $this->info('Adres: ' . $rep->prawAdresEmail);
            }
        } catch (InvalidUserKeyException $e) {
            echo 'Bad user key';
        } catch (NotFoundException $e) {
            echo 'No data found <br>';
            echo 'For more information read server message below: <br>';
            echo sprintf(
                "StatusSesji:%s\nKomunikatKod:%s\nKomunikatTresc:%s\n",
                $gus->getSessionStatus(),
                $gus->getMessageCode(),
                $gus->getMessage()
            );
        }
    }
}
