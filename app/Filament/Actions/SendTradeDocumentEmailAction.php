<?php

namespace App\Filament\Actions;

use App\Jobs\SendTradeDocEmailJob;
use App\Models\TradeDoc;
use App\Repositories\TenantRepository;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SendTradeDocumentEmailAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->name('send_email');
        $this->label('Wyślij na email');
        $this->icon('heroicon-o-envelope');
        $this->color('primary');
        $this->modalWidth(MaxWidth::Medium);
        $this->modalHeading('Wyślij dokument email');
        $this->modalDescription('Wyślij dokument handlowy na adres email');

        $this->form([
            TextInput::make('recipient_email')
                ->label('Adres email odbiorcy')
                ->email()
                ->required()
                ->default(function (Model $record) {
                    if ($record instanceof TradeDoc && $record->buyer) {
                        return $record->buyer->email;
                    }
                    return null;
                })
                ->helperText('Adres email został automatycznie wypełniony danymi nabywcy. Możesz go zmienić.'),

            Checkbox::make('send_to_me')
                ->label('Wyślij kopię do mnie')
                ->helperText('Zaznacz, aby otrzymać kopię wiadomości na swój adres email')
                ->default(false),
        ]);

        $this->action(function (array $data, Model $record) {
            if (!$record instanceof TradeDoc) {
                Notification::make()
                    ->title('Błąd')
                    ->body('Nieprawidłowy typ dokumentu')
                    ->danger()
                    ->send();
                return;
            }

            $validator = Validator::make($data, [
                'recipient_email' => 'required|email',
                'send_to_me' => 'boolean',
            ]);

            if ($validator->fails()) {
                Notification::make()
                    ->title('Błąd walidacji')
                    ->body('Sprawdź poprawność wprowadzonych danych')
                    ->danger()
                    ->send();
                return;
            }

            try {
                $currentUserEmail = null;
                if ($data['send_to_me'] ?? false) {
                    $currentUserEmail = auth()->user()?->email ?? null;
                }

                if (!TenantRepository::incrementEmailSentLimit(tenant())) {
                    $minutes = ceil(TenantRepository::getEmailSentLimitTTL(tenant()) / 60);
                    Notification::make()
                        ->title('Limit wysyłania emaili')
                        ->body('Osiągnąłeś godzinowy limit wysyłania emaili. Spróbuj ponownie za ' .
                            $minutes . ' minut.')
                        ->danger()
                        ->send();
                    Notification::make()
                        ->title('Limit wysyłania emaili')
                        ->body('Osiągnąłeś godzinowy limit wysyłania emaili. Spróbuj ponownie za ' .
                            $minutes . ' minut.')
                        ->danger()
                        ->sendToDatabase(auth()->user());
                    return;
                }

                SendTradeDocEmailJob::dispatch($record, $data['recipient_email'], $currentUserEmail)
                    ->onQueue('emails');

                Notification::make()
                    ->title('Email wysłany')
                    ->body('Dokument został wysłany na adres: ' . $data['recipient_email'])
                    ->success()
                    ->send();
            } catch (\Exception $e) {
                Log::error('Failed to send trade document email', [
                    'trade_doc_id' => $record->uuid,
                    'recipient' => $data['recipient_email'],
                    'error' => $e->getMessage()
                ]);

                Notification::make()
                    ->title('Błąd wysyłania')
                    ->body('Wystąpił błąd podczas wysyłania emaila. Spróbuj ponownie.')
                    ->danger()
                    ->send();
            }
        });
    }
}
