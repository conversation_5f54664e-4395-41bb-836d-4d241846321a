<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TenantResource\Pages;
use App\Filament\Resources\TenantResource\RelationManagers;
use App\Models\Tenant;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Facades\FilamentIcon;
use Filament\Support\Icons\IconManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('hash')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('name')
                    ->formatStateUsing(fn($state) => Str::limit($state, 40))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('vat_id')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('has_active_subscription')
                    ->label('Subskrypcja')
                    ->default(true)
                    ->color(fn($record) => match ($record->hasActiveSubscription()) {
                        true => 'success',
                        default => 'warning'
                    })
                    ->icon(fn($record) => match ($record->hasActiveSubscription()) {
                        false => 'heroicon-o-x-circle',
                        default => 'heroicon-o-check-circle'
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('Y-m-d')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active'),
                Tables\Filters\TernaryFilter::make('subscription_id')
                ->label('Subskrypcja')
                ->queries(
                    true: fn(Builder $query) => $query->whereNotNull('subscription_id'),
                    false: fn(Builder $query) => $query->whereNull('subscription_id'),
                    blank: fn(Builder $query) => $query
                )
                ->indicateUsing(fn($state) => match ($state['value']) {
                    '1' => 'Aktywna subskrypcja',
                    '0' => 'Bez aktywnej subskrypcji',
                    default => 'Wszystkie'
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->hiddenLabel(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\UserRelationManager::class,
            RelationManagers\WarehouseRelationManager::class,
            RelationManagers\SubscriptionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
        ];
    }
}
