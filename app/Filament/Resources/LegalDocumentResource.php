<?php

namespace App\Filament\Resources;

use App\Enums\LegalDocumentStatus;
use App\Enums\LegalDocumentType;
use App\Filament\Resources\LegalDocumentResource\Pages;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class LegalDocumentResource extends Resource
{
    protected static ?string $model = LegalDocument::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Dokumenty prawne';

    protected static ?string $modelLabel = 'Dokumenty prawne';

    protected static ?string $pluralModelLabel = 'Dokumenty prawne';


    public static function canAccess(): bool
    {
        return auth()->user()?->isGod() ?? false;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()?->isGod() ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informacje podstawowe')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('document_type')
                                    ->label('Typ')
                                    ->options(LegalDocumentType::options())
                                    ->required()
                                    ->disabled(fn(?LegalDocument $record) => $record && !$record->isEditable()),

                                Forms\Components\TextInput::make('title')
                                    ->label('Tytuł')
                                    ->required()
                                    ->maxLength(255)
                                    ->disabled(fn(?LegalDocument $record) => $record && !$record->isEditable()),
                            ]),

                        Forms\Components\Placeholder::make('status_info')
                            ->label('Informacje')
                            ->content(function (?LegalDocument $record): HtmlString {
                                if (!$record) {
                                    return new HtmlString(
                                        '<span class="text-sm text-gray-600">Nowy dokument roboczy</span>'
                                    );
                                }


                                $info = "Status: {$record->status->label()}<br>";
                                $info .= "Wersja: {$record->version_number}<br>";

                                if ($record->publication_date) {
                                    $publishedDate = $record
                                        ->publication_date
                                        ->timezone('Europe/Warsaw')
                                        ->format('Y-m-d H:i');
                                    $info .= "Opublikowany: {$publishedDate}<br>";
                                }

                                $updatedDate = $record->updated_at->timezone('Europe/Warsaw')->format('Y-m-d H:i');
                                $info .= "Ostatnia aktualizacja: {$updatedDate}";

                                return new HtmlString($info);
                            }),
//                            ->visible(fn(?LegalDocument $record) => $record !== null),
                    ]),

                Forms\Components\Section::make('Treść dokumentu')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->label('')
                            ->hiddenLabel()
                            ->required()
                            ->extraInputAttributes(
                                ['style' => 'min-height: 20rem; max-height: 80vh; overflow-y: auto;']
                            )
                            ->columnSpanFull()
                            ->disabled(fn(?LegalDocument $record) => $record && !$record->isEditable())
                            ->helperText('Wprowadź treść dokuemtu')
                            ->toolbarButtons([
                                'h2',
                                'h3',
                                'bold',
                                'italic',
                                'strike',
                                'underline',
                                'codeBlock',
                                'blockquote',
                                'link',
                                'bulletList',
                                'orderedList',
                                'redo',
                                'undo',
                            ])
                        ,
                    ]),

                Forms\Components\Section::make('Generated Files')
                    ->schema([
                        Forms\Components\Placeholder::make('file_links')
                            ->label('Generated Files')
                            ->content(function (?LegalDocument $record): HtmlString {
                                if (!$record || !$record->isPublished()) {
                                    return new HtmlString('<span class="text-sm text-gray-600">Files will be generated when the document is published</span>');
                                }

                                $links = '';
                                if ($record->getHtmlUrl()) {
                                    $links .= '<a href="' . $record->getHtmlUrl() . '" target="_blank" class="inline-flex items-center px-3 py-2 border text-sm leading-4 font-medium rounded-md dark:text-white bg-teal-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">View HTML</a>';
                                }
                                if ($record->getPdfUrl()) {
                                    $links .= '<a href="' . $record->getPdfUrl() . '" target="_blank" class="inline-flex items-center px-3 py-2 border text-sm leading-4 font-medium rounded-md dark:text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">Download PDF</a>';
                                }

                                return new HtmlString($links ?: '<span class="text-sm text-gray-600">No files available</span>');
                            })
                    ])
                    ->visible(fn(?LegalDocument $record) => $record && $record->isPublished()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('document_type')
                    ->label('Typ')
                    ->badge()
                    ->formatStateUsing(fn(LegalDocumentType $state) => $state->value)
                    ->color(fn($state) => $state->color())
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Tytuł')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(LegalDocumentStatus $state) => $state->label())
                    ->color(fn(LegalDocumentStatus $state) => $state->color())
                    ->sortable(),

                Tables\Columns\TextColumn::make('version_number')
                    ->label('Wersja')
                    ->sortable(),

                Tables\Columns\TextColumn::make('publication_date')
                    ->label('Opublikowany')
                    ->dateTime('d-m-Y')
                    ->sortable()
                    ->placeholder('Nie opublikowany'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Aktualizacja')
                    ->dateTime('d-m-Y')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('document_type')
                    ->label('Typ dokumentu')
                    ->options(LegalDocumentType::options()),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(LegalDocumentStatus::options()),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn(LegalDocument $record) => $record->isEditable()),

                Tables\Actions\Action::make('clone')
                    ->label('Klonuj')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('warning')
                    ->visible(fn(LegalDocument $record) => $record->isPublished())
                    ->requiresConfirmation()
                    ->modalHeading('Tworzy nową wersję dokumentu')
                    ->modalDescription('Dokument zostanie otworzony do edycji.')
                    ->action(function (LegalDocument $record) {
                        $newDraft = LegalDocumentRepository::cloneToNewDraft($record);

                        Notification::make()
                            ->title('Utworzono dokument')
                            ->body("Nowa wersja {$newDraft->version_number} robocza utworzona.")
                            ->success()
                            ->send();

                        return redirect(static::getUrl('edit', ['record' => $newDraft]));
                    }),

                Tables\Actions\Action::make('publish')
                    ->label('Publikuj')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn(LegalDocument $record) => $record->canBePublished())
                    ->requiresConfirmation()
                    ->modalHeading('Publikuj Dokument')
                    ->modalDescription('Ta wersja zostanie opublikowana i stanie się wersją aktualną.
                    Pozostałe wersje staną się archiwalne.')
                    ->action(function (LegalDocument $record) {
                        if (LegalDocumentRepository::publishDraft($record)) {
                            Notification::make()
                                ->title('Dokument został opublikowany')
                                ->body('Dokument w wersji ' .
                                    $record->version_number .
                                    ' został opublikowany i pliki zostały utworzone.')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Nie udalo się opublikować dokumentu')
                                ->body(LegalDocumentRepository::$error)
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('preview')
                    ->label('Podgląd')
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->url(fn(LegalDocument $record) => route('legal.preview', $record))
                    ->openUrlInNewTab(),
            ])
            ->modifyQueryUsing(function (Builder $query, Table $table) {
                if (($table->getFilter('status')->getState()['value'] ?? null) === LegalDocumentStatus::ARCHIVED->value) {
                    return $query;
                }
                return $query->whereNot('status', LegalDocumentStatus::ARCHIVED);
            })
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => auth()->user()?->isTenantAdmin() ?? false),
                ]),
            ])
            ->recordUrl(null)
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLegalDocuments::route('/'),
            'create' => Pages\CreateLegalDocument::route('/create'),
            'view' => Pages\ViewLegalDocument::route('/{record}'),
            'edit' => Pages\EditLegalDocument::route('/{record}/edit'),
        ];
    }
}
