<?php

namespace App\Filament\Resources;

use App\Enums\FaqStatus;
use App\Filament\Resources\FaqResource\Pages;
use App\Models\Faq;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class FaqResource extends Resource
{
    protected static ?string $model = Faq::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

//    protected static ?string $navigationGroup = 'Zawartość';

    protected static ?string $navigationLabel = 'FAQ';

    protected static ?string $modelLabel = 'FAQ';

    protected static ?string $pluralModelLabel = 'FAQ';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Podstawowe informacje')
                    ->schema([
                        Forms\Components\TextInput::make('question')
                            ->label('Pytanie')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('category')
                            ->label('Kategoria')
                            ->required()
                            ->options(function () {
                                $categories = Faq::getCategories();
                                return array_combine($categories, $categories);
                            })
                            ->searchable()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('category')
                                    ->label('Nowa kategoria')
                                    ->required()
                                    ->maxLength(255),
                            ])
                            ->createOptionUsing(function (array $data) {
                                return $data['category'];
                            }),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(FaqStatus::options())
                            ->default(FaqStatus::DRAFT->value)
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Odpowiedź')
                    ->schema([
                        Forms\Components\RichEditor::make('answer')
                            ->label('')
                            ->hiddenLabel()
                            ->required()
                            ->extraInputAttributes(
                                ['style' => 'min-height: 20rem; max-height: 80vh; overflow-y: auto;']
                            )
                            ->columnSpanFull()
                            ->helperText('Wprowadź odpowiedź na pytanie')
                            ->toolbarButtons([
                                'h2',
                                'h3',
                                'bold',
                                'italic',
                                'strike',
                                'underline',
                                'codeBlock',
                                'blockquote',
                                'link',
                                'bulletList',
                                'orderedList',
                                'redo',
                                'undo',
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question')
                    ->label('Pytanie')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('category')
                    ->label('Kategoria')
                    ->searchable()
                    ->sortable()
                    ->badge(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (FaqStatus $state): string => $state->color())
                    ->icon(fn (FaqStatus $state): string => $state->icon())
                    ->formatStateUsing(fn (FaqStatus $state): string => $state->label()),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Utworzono')
                    ->dateTime('d.m.Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Zaktualizowano')
                    ->dateTime('d.m.Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Kategoria')
                    ->options(function () {
                        $categories = Faq::getCategories();
                        return array_combine($categories, $categories);
                    }),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(FaqStatus::options()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('publish')
                        ->label('Opublikuj')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['status' => FaqStatus::PUBLISHED]);
                            });
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Opublikuj wybrane FAQ')
                        ->modalDescription('Czy na pewno chcesz opublikować wybrane FAQ?'),

                    Tables\Actions\BulkAction::make('draft')
                        ->label('Oznacz jako roboczy')
                        ->icon('heroicon-o-pencil')
                        ->color('warning')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['status' => FaqStatus::DRAFT]);
                            });
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Oznacz jako roboczy')
                        ->modalDescription('Czy na pewno chcesz oznaczyć wybrane FAQ jako robocze?'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFaqs::route('/'),
            'create' => Pages\CreateFaq::route('/create'),
            'view' => Pages\ViewFaq::route('/{record}'),
            'edit' => Pages\EditFaq::route('/{record}/edit'),
        ];
    }
}
