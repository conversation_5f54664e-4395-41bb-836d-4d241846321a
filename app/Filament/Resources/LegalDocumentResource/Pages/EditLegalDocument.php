<?php

namespace App\Filament\Resources\LegalDocumentResource\Pages;

use App\Filament\Resources\LegalDocumentResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;

class EditLegalDocument extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = LegalDocumentResource::class;

    public function getTitle(): string|Htmlable
    {
        return "Edycja: {$this->getRecord()->title}";
    }

    public function getHeading(): string|Htmlable
    {
        return "Edytuj dokument prawny";
    }

    public function getSubheading(): string|Htmlable|null
    {
        $record = $this->getRecord();
        return "Wersja {$record->version_number} • {$record->document_type->label()} • {$record->status->label()}";
    }

    protected function getHeaderActions(): array
    {
        $record = $this->getRecord();
        $actions = [];

        $actions[] = Actions\Action::make('preview')
            ->label('Podgląd')
            ->icon('heroicon-o-eye')
            ->color('gray')
            ->url(route('legal.preview', $record))
            ->openUrlInNewTab();

        if ($record->canBePublished()) {
            $actions[] = Actions\Action::make('publish')
                ->label('Publikuj')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Publikuj Dokument')
                ->modalDescription(
                    'Ta wersja zostanie opublikowana i stanie się wersją aktualną.
                    Pozostałe wersje staną się archiwalne.'
                )
                ->action(function (LegalDocument $record) {
                    if (LegalDocumentRepository::publishDraft($record)) {
                        Notification::make()
                            ->title('Dokument został opublikowany')
                            ->body('Dokument w wersji ' .
                                $record->version_number .
                                ' został opublikowany i pliki zostały utworzone.')
                            ->success()
                            ->send();

                        return redirect(self::getResource()::getUrl('view', ['record' => $this->getRecord()]));
                    }

                    Notification::make()
                        ->title('Nie udalo się opublikować dokumentu')
                        ->body(LegalDocumentRepository::$error)
                        ->danger()
                        ->send();
                });
        }

        if ($record->isEditable()) {
            $actions[] = Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading('Usunąć dokument roboczy?')
                ->modalDescription('Czy na pewno usunąć dokument roboczy? Ta czynność nie może być cofnięta.')
                ->successRedirectUrl(self::getResource()::getUrl('index'));
        }

        return $actions;
    }

    protected function getFormActions(): array
    {
        $actions = [];

        if ($this->getRecord()->isEditable()) {
            $actions[] = $this->getSaveFormAction();
        }

        $actions[] = self::getBackToListFormAction();

        return $actions;
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Dokument zapisany';
    }
}
