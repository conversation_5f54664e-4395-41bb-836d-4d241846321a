<?php

namespace App\Filament\Resources\LegalDocumentResource\Pages;

use App\Enums\LegalDocumentType;
use App\Filament\Resources\LegalDocumentResource;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class ListLegalDocuments extends ListRecords
{
    protected static string $resource = LegalDocumentResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Dokumenty prawne';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Dokumenty prawne';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Dokumenty prawne - zarządzanie';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Utwórz nowy')
                ->icon('heroicon-o-plus'),
        ];
    }


    protected function getHeaderWidgets(): array
    {
        return [];
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
