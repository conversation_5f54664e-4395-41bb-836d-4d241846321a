<?php

namespace App\Filament\Resources\LegalDocumentResource\Pages;

use App\Filament\Resources\LegalDocumentResource;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ViewLegalDocument extends ViewRecord
{
    protected static string $resource = LegalDocumentResource::class;

    public function getTitle(): string|Htmlable
    {
        return $this->getRecord()->title;
    }

    public function getHeading(): string|Htmlable
    {
        return 'Właś<PERSON>wości dokumentu';
    }

    public function getSubheading(): string|Htmlable|null
    {
        $record = $this->getRecord();
        return "Wersja {$record->version_number} • {$record->document_type->label()} • {$record->status->label()}";
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informacje')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('document_type')
                                    ->label('Typ')
                                    ->formatStateUsing(fn($state) => $state->label())
                                    ->badge()
                                    ->color(fn($state) => $state->color()),

                                TextEntry::make('status')
                                    ->label('Status')
                                    ->formatStateUsing(fn($state) => $state->label())
                                    ->badge()
                                    ->color(fn($state) => $state->color()),

                                TextEntry::make('version_number')
                                    ->label('Wersja'),

                                TextEntry::make('publication_date')
                                    ->label('Data publikacji')
                                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw')
                                    ->placeholder('nie opublikowany'),

                                TextEntry::make('created_at')
                                    ->label('Utworzono')
                                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw'),

                                TextEntry::make('updated_at')
                                    ->label('Aktualizowano')
                                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw'),
                            ]),
                    ]),

                Section::make('Wygenerowane pliki')
                    ->schema([
                        TextEntry::make('file_paths')
                            ->label(fn() => 'Dostępne pliki')
                            ->default(function (LegalDocument $record) {
                                if (!$record->isPublished()) {
                                    return new HtmlString(
                                        '<span class="text-sm text-gray-600">
                                                Pliki zostaną wygenerowane po opublikowaniu dokumentu.
                                                </span>'
                                    );
                                }

                                $links = '';
                                if ($record->getHtmlUrl()) {
                                    $links .= '<a href="' . $record->getHtmlUrl() . '" target="_blank"
                                                class="inline-flex
                                                items-center
                                                px-3
                                                py-2
                                                border
                                                text-sm
                                                leading-4
                                                font-medium rounded-md
                                                dark:text-white
                                                bg-blue-600
                                                hover:bg-blue-700
                                                focus:outline-none
                                                focus:ring-2
                                                focus:ring-offset-2 focus:ring-blue-500 mr-2 mb-2">
                                                View HTML</a>';
                                }
                                if ($record->getPdfUrl()) {
                                    $links .= '<a href="' . $record->getPdfUrl() . '" target="_blank"
                                                 class="inline-flex items-center px-3 py-2
                                                 border text-sm leading-4 font-medium rounded-md
                                                 dark:text-white bg-pink-600 hover:bg-pink-700
                                                 focus:outline-none focus:ring-2 focus:ring-offset-2
                                                 focus:ring-pink-500 mb-2">
                                                 Download PDF
                                                 </a>';
                                }

                                return new HtmlString(
                                    $links ?: '<span class="text-sm text-gray-600">No files available</span>'
                                );
                            })
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
