<?php

namespace App\Filament\Resources\LegalDocumentResource\Pages;

use App\Filament\Resources\LegalDocumentResource;
use App\Filament\Traits\HasBackToListButton;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;

class CreateLegalDocument extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = LegalDocumentResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Utwórz nowy dokument prawny';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Utwórz nowy dokument prawny';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return null;
    }

    protected function handleRecordCreation(array $data): Model
    {
        return parent::handleRecordCreation($data);
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit', ['record' => $this->getRecord()]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
//            $this->getCreateAnotherFormAction(),
            self::getBackToListFormAction(),
        ];
    }
}
