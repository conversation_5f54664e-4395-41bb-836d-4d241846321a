<?php

namespace App\Filament\App\Pages;

use App\Helpers\UserHelper;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;

class EditProfile extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.edit-profile';

    protected static bool $shouldRegisterNavigation = false;

    use InteractsWithForms;

    public ?array $profileData = [];
    public ?array $passwordData = [];

    public ?array $additionalData = [];

    public $profile;

    public function mount(): void
    {
        $this->fillForms();
    }

    public function getHeading(): string|Htmlable
    {
        return 'Edytuj profil';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('delete')
                ->label('Usuń konto')
                ->color('danger')
                ->requiresConfirmation()
                ->modalDescription(fn() => $this->getDeleteDescriptionText())
                ->form([
                    TextInput::make('email')
                        ->label('Email')
                        ->helperText('Wpisz swój adres email aby potwierdzić usunięcie konta')
                        ->email()
                        ->same('source_email')
                        ->validationMessages([
                            'same' => 'Adres email nie jest poprawny.'
                        ])
                        ->required(),
                    Hidden::make('source_email')->default(fn() => $this->profile->email),
                ])
                ->action(function ($data) {
                    if ($data['email'] !== $this->profile->email) {
                        Notification::make()
                            ->title('Adres email nie jest poprawny!')
                            ->danger()
                            ->send();
                        return false;
                    }
                    try {
                        dispatch(new \App\Jobs\AccountDeleteJob($this->profile));
                        event(new \App\Events\AccountDeleted($this->profile));
                        Filament::auth()->logout();
                        session()->invalidate();
                        session()->regenerateToken();
                        return true;
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Nie udało się usunąć konta')
                            ->danger()
                            ->send();
                        return false;
                    }
                })
            ->after(fn() => $this->redirect(filament()->getLoginUrl(), true))
        ];
    }

    protected function getDeleteDescriptionText(): string|Htmlable
    {
        return new HtmlString('Usunięcie Twojego konta <br>
        spowoduje usunięcie wszystkich Twoich danych oraz danych Twojej firmy! <br>
        Ta czynność jest nieodwracalna!');
    }

    protected function getForms(): array
    {
        return [
            'editProfileForm',
            'editPasswordForm',
            'editProfileAdditionalDataForm'
        ];
    }

    public function editProfileForm(Form $form): Form
    {
        return
            $form->schema([
                Section::make('Dane podstawowe')
                    ->schema([
                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true),
                    ])->columns(2),
            ])
                ->model($this->getUser())
                ->statePath('profileData');
    }

    public function editPasswordForm(Form $form): Form
    {
        return $form->schema([
            Section::make('Zmień hasło')
                ->description('Wprowadź nowe hasło jeżeli chcesz je zmienić')
                ->schema([
                    TextInput::make('password')
                        ->label(__('app._.password'))
                        ->password()
                        ->confirmed()
                        ->maxLength(255),
                    TextInput::make('password_confirmation')
                        ->label(__('app._.password_confirmation'))
                        ->password()
                        ->requiredWith('password')
                        ->maxLength(255),
                ]),
        ])
            ->model($this->getUser())
            ->statePath('passwordData');
    }

    public function editProfileAdditionalDataForm(Form $form): Form
    {
        return $form->schema([
            Section::make(__('app.users.profile.section_heading'))
                ->description(__('app.users.profile.section_description'))
                ->columns(3)
                ->schema([
                    TextInput::make('name')
                        ->label(__('app.users.profile.name'))
                        ->maxLength(255),
                    TextInput::make('surname')
                        ->label(__('app.users.profile.surname'))
                        ->maxLength(255),
                    TextInput::make('number')
                        ->label(__('app.users.profile.number'))
                        ->maxLength(255)
                        ->numeric(),
                ])
        ])->statePath('additionalData');
    }

    protected function getUpdateProfileFormActions(): array
    {
        return [
            Action::make('updateProfileAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editProfileForm'),
        ];
    }

    protected function getUpdatePasswordFormActions(): array
    {
        return [
            Action::make('updatePasswordAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editPasswordForm'),
        ];
    }

    protected function getUpdateProfileAdditionalDataActions(): array
    {
        return [
            Action::make('updateProfileAdditionalDataAction')
                ->label(__('filament-panels::pages/auth/edit-profile.form.actions.save.label'))
                ->submit('editProfileAdditionalDataForm'),
        ];
    }


    protected function getUser(): Model
    {
        $user = auth()->user();
        $this->profile = $user;
        return $user;
    }

    protected function getUserData(): ?Model
    {
        return auth()->user()?->profile()->first();
    }

    protected function fillForms(): void
    {
        $data = $this->getUser()->attributesToArray();
        $this->editProfileForm->fill($data);
        $this->editPasswordForm->fill();
        $additionalData = $this->getUserData();
        $this->editProfileAdditionalDataForm->fill($additionalData?->attributesToArray() ?? []);
    }

    /**
     * Called from szablon xDd
     * @return void
     */
    public function updateAdditionalData(): void
    {
        $data = $this->editProfileAdditionalDataForm->getState();
        $data['lang'] = 'pl';
        if (UserHelper::editUserAdditionalData(auth()->user(), $data)) {
            Notification::make()
                ->title('Zapisano pomyślnie')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Problem z zapisem danych')
                ->danger()
                ->send();
        }
    }

    public function updateProfile(): void
    {
        $data = $this->editProfileForm->getState();
        $model = auth()->user();
        if ($model->update($data)) {
            Notification::make()
                ->title('Zapisano pomyślnie')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Problem z zapisem danych')
                ->danger()
                ->send();
        }
    }

    public function updatePassword()
    {
        $data = $this->editPasswordForm->getState();
        if ($data['password'] === $data['password_confirmation']) {
            if (auth()->user()->update([
                'password' => Hash::make($data['password'])
            ])) {
                Notification::make()
                    ->title('Zapisano pomyślnie')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Problem z zapisem danych')
                    ->danger()
                    ->send();
            }
        } else {
            Notification::make()
                ->title('Hasła nie są identyczne !')
                ->danger()
                ->send();
        }
    }
}
