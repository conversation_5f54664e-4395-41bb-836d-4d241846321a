<?php

namespace App\Filament\App\Pages;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Models\DTOTenantMetadata;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Tenant;
use App\Repositories\SubscriptionsRepository as SubRepository;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section as FormSection;
use Filament\Forms\Get;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class SubscriptionManagement extends Page
{
    use InteractsWithForms, InteractsWithInfolists;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static string $view = 'filament.app.pages.subscription-management';
    protected static ?string $navigationGroup = 'Ustawienia';
    protected static ?int $navigationSort = 10;

    public ?Tenant $tenant = null;
    public ?Subscription $currentSubscription = null;
    public Collection $availablePlans;
    protected ?DTOTenantMetadata $tenantMetadata = null;

    public bool $redirect = false;
    public string $redirectUrl = '';

    public bool $trialPlanUsed = false;

    // Properties for renewal payment handling
    public bool $isPaymentFinished = false;
    public ?string $continuePaymentUrl = null;
    public ?Subscription $pendingSubscription = null;
    public ?Payment $pendingPayment = null;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public static function getNavigationLabel(): string
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Zarządzanie subskrypcją';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return 'Zarządzaj swoją subskrypcją i wybierz plan odpowiadający Twoim potrzebom';
    }

    public function mount(): void
    {
        $this->tenant = auth()->user()->getTenant();
        $this->loadCurrentSubscription();
        $this->trialPlanUsed();
        $this->loadAvailablePlans();
        $this->loadTenantMetadata();
        $this->pendingSubscription = $this->hasPendingSubscription();
        $this->pendingPayment = $this->hasPendingPayment();
    }

    protected function trialPlanUsed()
    {
        if ($this->currentSubscription !== null && $this->currentSubscription->plan->type !== PlanType::TRIAL) {
            $this->trialPlanUsed = true;
            return;
        }

        if ($this->currentSubscription === null && tenant()->subscriptions()->count() > 0) {
            $this->trialPlanUsed = true;
            return;
        }

        $this->trialPlanUsed = Subscription::where('tenant_id', $this->tenant->id)
                ->where('plan_id', 1)
                ->whereNotNull('ended_at')
                ->count() > 0;
    }

    protected function hasPendingSubscription(): ?Subscription
    {
        return $this->tenant?->getPendingSubscription();
    }

    protected function hasPendingPayment(): ?Payment
    {
        return $this->tenant?->getPendingPayments();
    }

    protected function trialPlansAvailable(): bool
    {
        return $this->tenant?->subscription?->selected_at ? true : false;
    }

    protected function loadCurrentSubscription(): void
    {
        $this->currentSubscription = $this->tenant?->currentSubscriptions()->first();
    }

    protected function loadAvailablePlans(): void
    {
        $this->availablePlans = Plan::where('is_active', true)
            ->orderBy('price')
            ->get();

        if (($this->trialPlanUsed && $this->currentSubscription?->plan->type !== PlanType::TRIAL) ||
            ($this->trialPlanUsed && $this->currentSubscription?->plan->type === null)
        ) {
            $this->availablePlans = $this->availablePlans->reject(fn($plan) => $plan->type === PlanType::TRIAL);
        }
    }

    protected function loadTenantMetadata(): void
    {
        $metaData = $this->tenant?->meta?->meta ?? [];

        if (!empty($metaData)) {
            try {
                $this->tenantMetadata = DTOTenantMetadata::make($metaData);
            } catch (\Exception $e) {
                $this->tenantMetadata = null;
            }
        }
    }

    protected function getRenewalPlanOptions(): array
    {
        return $this->availablePlans
            ->filter(fn($plan) => $plan->type !== PlanType::TRIAL)
            ->mapWithKeys(function (Plan $plan) {
                $periodDeclination = $this->monthsDeclination($plan->period->value);

                $priceText = number_format($plan->price, 2, ',', ' ') .
                    ' PLN / ' .
                    $plan->period->value .
                    ' ' .
                    $periodDeclination;

                $isSame = SubRepository::isSamePlan($plan, $this->currentSubscription);
                $label = $plan->name .
                    ' - ' .
                    $priceText . ($isSame ? ' <strong>(kontynuacja)</strong>' : ' <strong>(zmiana planu)</strong>');

                return [$plan->id => new HtmlString($label)];
            })->toArray();
    }

    protected function getRenewalPlanDescriptions(): array
    {
        return $this->availablePlans
            ->filter(fn($plan) => $plan->type !== PlanType::TRIAL)
            ->mapWithKeys(function (Plan $plan) {
                $features = $this->formatPlanFeatures($plan->features ?? []);
                $startDate = SubRepository::calculateRenewalStartDate($plan, $this->currentSubscription);

                $description = $plan->description .
                    ". Data rozpoczęcia: <strong style='color: black;'>" .
                    $startDate->format('d.m.Y') .
                    "</strong><br>";
                $description .= "Dostępne funkcje: " . implode(', ', $features) . "<br>";
                if (!SubRepository::isSamePlan($plan, $this->currentSubscription)) {
                    $diff = $this->findPlansDifferences(
                        $this->currentSubscription->plan->features ?? [],
                        $plan->features ?? []
                    );

                    if (($plan->weight < $this->currentSubscription->plan->weight) && !empty($diff)) {
                        $description .= "<strong style='color: red;'>
                                        Utracisz dostęp do pewnych funkcji aplikacji:
                                        </strong> " .
                            implode(', ', $this->formatPlanFeatures($diff)) .
                            "<br>";
                    } elseif (($plan->weight > $this->currentSubscription->plan->weight) && !empty($diff)) {
                        $description .= "<strong style='color: green;'>
                                        Dostaniesz dostęp do dodatkowych funkcji aplikacji:
                                        </strong> " .
                            implode(', ', $this->formatPlanFeatures($diff)) .
                            " <br>";
                    }
                }

                return [$plan->id => new HtmlString($description)];
            })->toArray();
    }

    protected function createRenewalSubscription(Plan $plan): void
    {
        $subRepo = new SubRepository();
        $subRepo->renewSubscriptionWithPayment(auth()->user(), $plan, $this->currentSubscription);
        $this->isPaymentFinished = $subRepo->isPaymentFinished;
        $this->continuePaymentUrl = $subRepo->continuePaymentUrl;
    }

    public function currentSubscriptionInfolist(Infolist $infolist): Infolist
    {
        if (!$this->currentSubscription) {
            return $infolist->schema([
                Section::make('Brak aktywnej subskrypcji')
                    ->description('Nie masz obecnie aktywnej subskrypcji. Wybierz plan poniżej, aby rozpocząć.')
                    ->schema([])
            ]);
        }

        return $infolist
            ->record($this->currentSubscription)
            ->schema([
                Section::make('Aktualna subskrypcja')
                    ->description('Szczegóły Twojej obecnej subskrypcji')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('plan.name')
                                    ->label('Plan')
                                    ->formatStateUsing(
                                        fn($state, $record) => $state . ' (subskrypcja: ' . $record->getOrderId() . ')'
                                    )
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        SubscriptionStatus::ACTIVE => 'success',
                                        SubscriptionStatus::NEW => 'warning',
                                        SubscriptionStatus::TRIAL => 'info',
                                        SubscriptionStatus::EXPIRED => 'danger',
                                        SubscriptionStatus::CANCELED => 'gray',
                                        default => 'gray'
                                    })
                                    ->formatStateUsing(fn($state) => $state->label()),
                                TextEntry::make('price')
                                    ->label('Cena')
                                    ->formatStateUsing(
                                        fn($state) => number_format($state, 2, ',', ' ') . ' PLN'
                                    ),
                                TextEntry::make('starts_at')
                                    ->label('Data rozpoczęcia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('ends_at')
                                    ->label('Data zakończenia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('plan.period')
                                    ->label('Okres')
                                    ->formatStateUsing(fn($state) => $state->value . ' miesięcy'),
                            ]),
                        TextEntry::make('plan.description')
                            ->label('Opis planu')
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($record->plan->description)),
                        TextEntry::make('pendingSubscription')
                            ->label('Plan oczekujący')
                            ->badge()
                            ->color('success')
                            ->state(fn() => 'Masz opłacony kolejny plan: ' . $this->pendingSubscription->plan->name)
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($this->pendingSubscription)),
                    ])
            ]);
    }

    public function unpaidSubscriptionInfolist(Infolist $infolist): Infolist
    {
        if (!$this->pendingPayment) {
            return $infolist->schema([]);
        }

        return $infolist
            ->record($this->pendingPayment->subscription)
            ->schema([
                Section::make('Nieopłacona Subskrypcja')
                    ->description('Szczegóły Twojej nieopłaconej subskrypcji')
                    ->headerActions([fn() => array_map(fn($action) => $action, $this->getSectionHeaderActions())])
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('plan.name')
                                    ->label('Plan')
                                    ->formatStateUsing(
                                        fn($state, $record) => $state . ' (subskrypcja: ' . $record->getOrderId() . ')'
                                    )
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        SubscriptionStatus::ACTIVE => 'success',
                                        SubscriptionStatus::NEW => 'warning',
                                        SubscriptionStatus::TRIAL => 'info',
                                        SubscriptionStatus::EXPIRED => 'danger',
                                        SubscriptionStatus::CANCELED => 'gray',
                                        default => 'gray'
                                    })
                                    ->formatStateUsing(fn($state) => $state->label()),
                                TextEntry::make('price')
                                    ->label('Cena')
                                    ->formatStateUsing(
                                        fn($state) => number_format($state, 2, ',', ' ') . ' PLN'
                                    ),
                                TextEntry::make('starts_at')
                                    ->label('Data rozpoczęcia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('ends_at')
                                    ->label('Data zakończenia')
                                    ->date('d.m.Y', 'Europe/Warsaw'),
                                TextEntry::make('plan.period')
                                    ->label('Okres')
                                    ->formatStateUsing(fn($state) => $state->value . ' miesięcy'),
                            ]),
                        TextEntry::make('plan.description')
                            ->label('Opis planu')
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($record->plan->description)),
                        TextEntry::make('pendingSubscription')
                            ->label('Plan oczekujący')
                            ->badge()
                            ->color('success')
                            ->state(fn() => 'Masz opłacony kolejny plan: ' . $this->pendingSubscription->plan->name)
                            ->columnSpanFull()
                            ->hidden(fn($record) => empty($this->pendingSubscription)),
                    ])
            ]);
    }

    protected function getHeaderActions(): array
    {
        $actions = [];

//        if ($this->shouldShowContinuePaymentButton()) {
//            $latestPayment = $this->pendingPayment;
//
//            if ($latestPayment && $latestPayment->getFinishPaymentLink()) {
//                $actions[] = Action::make('continuePayment')
//                    ->label('Kontynuuj płatność')
//                    ->icon('heroicon-o-credit-card')
//                    ->color('warning')
//                    ->url($latestPayment->getFinishPaymentLink())
//                    ->openUrlInNewTab();
//                $actions[] = Action::make('deletePayment')
//                    ->label('Anuluj płatność')
//                    ->icon('heroicon-o-credit-card')
//                    ->requiresConfirmation()
//                    ->color('danger')
//                    ->action(fn() => $this->deletePayment($latestPayment))
//                    ->after(fn() => $this->redirect(self::getUrl(), true));
//            }
//        }

        if ($this->shouldShowRenewalButton()) {
            $actions[] = Action::make('renewSubscription')
                ->label('Przedłuż Subskrypcję')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->form([
                    FormSection::make('Wybierz plan do przedłużenia')
                        ->description('Wybierz plan, na który chcesz przedłużyć swoją subskrypcję')
                        ->schema([
                            Radio::make('selected_plan_id')
                                ->label('')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Wybór planu jest wymagany.',
                                ])
                                ->options($this->getRenewalPlanOptions())
                                ->descriptions($this->getRenewalPlanDescriptions())
                                ->columns(1)
                                ->default($this->currentSubscription->plan_id),
                            Checkbox::make('agree_on_payment')
                                ->label('Zamawiam i płacę za subskrypcję')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Zaznaczenie opcji jest konieczne.',
                                ])
                                ->helperText('Rozumiem, że potwierdzenie zamówienia wiąże się
                                z obowiązkiem opłacenia subskrypcji.
                            Zostaniesz przekierowany na stronę pośrednika w celu dokonania płatności.'),
                        ]),
                ])
                ->modalHeading('Przedłuż subskrypcję')
                ->modalSubmitActionLabel('Przedłuż subskrypcję')
                ->modalWidth('4xl')
                ->action(function (array $data) {
                    $selectedPlan = Plan::findOrFail($data['selected_plan_id']);

                    try {
                        $this->createRenewalSubscription($selectedPlan);

                        if ($this->isPaymentFinished) {
                            tenant(true);
                            Notification::make()
                                ->title('Subskrypcja została przedłużona')
                                ->body('Twoja subskrypcja została pomyślnie przedłużona.')
                                ->success()
                                ->send();
                            $this->redirect(self::getUrl());
                            return;
                        }

                        if ($this->continuePaymentUrl) {
                            $this->redirectUrl = $this->continuePaymentUrl;
                            $this->redirect = true;
                        }
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Błąd podczas przedłużania subskrypcji')
                            ->body('Wystąpił błąd podczas przedłużania subskrypcji. Spróbuj ponownie.')
                            ->danger()
                            ->send();
                    }
                })
                ->after(fn() => $this->redirect ? $this->redirect($this->redirectUrl) : true);
        }

        if ($this->shouldShowCancelButton()) {
            $actions[] = Action::make('cancelSubscription')
                ->label('Anuluj subskrypcję')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Anuluj subskrypcję')
                ->modalDescription(new HtmlString('Czy na pewno chcesz anulować swoją subskrypcję?
<br>Ta akcja nie może zostać cofnięta.'))
                ->modalSubmitActionLabel('Tak, anuluj')
                ->form([
                    Checkbox::make('agree_on_cancellation')
                        ->label('Anuluj moją subskrypcję')
                        ->required()
                        ->validationMessages([
                            'required' => 'Zaznaczenie opcji jest konieczne.',
                        ])
                        ->helperText('Rozumiem, że anulowanie subskrypcji nie wiąże sie ze zwrotem poniesionych opłat.
                    Subskrypcja zostanie anulowana natychmiast.
                    Konto pozostające bez aktywnej subskrypcji przez 14 dni zostanie automatycznie usunięte.'),
                ])
                ->action(function () {
                    $subRepo = new SubRepository();
                    $subRepo->cancelSubscription($this->currentSubscription);
                    tenant(true);
                    Notification::make()
                        ->title('Subskrypcja została anulowana')
                        ->success()
                        ->sendToDatabase(auth()->user());
                })
                ->after(fn() => $this->redirect(self::getUrl()));
        }

        return $actions;
    }

    public function getAvailablePlansData(): array
    {

        if ($this->shouldShowRenewalButton()) {
            $missingFeatures = $this->formatPlanFeatures(
                $this->findMissingFeatures(
                    $this->currentSubscription->plan->features ?? []
                )
            );
            $features = $this->formatPlanFeatures($this->currentSubscription->plan->features ?? []);
            $periodDeclination = $this->monthsDeclination($this->currentSubscription->plan->period->value);

            return [
                [
                    'id' => $this->currentSubscription->plan->id,
                    'name' => $this->currentSubscription->plan->name,
                    'description' => $this->currentSubscription->plan->description,
                    'price' => number_format($this->currentSubscription->plan->price, 2, ',', ' ') . ' PLN',
                    'period' => $this->currentSubscription->plan->period->value . ' ' . $periodDeclination,
                    'features' => $features,
                    'missing_features' => $missingFeatures,
                    'weight' => $this->currentSubscription->plan->weight,
                    'is_current' => true,
                    'type' => $this->currentSubscription->plan->type->label(),
                ]
            ];
        }

        return $this->availablePlans->map(function (Plan $plan) {
            $features = $this->formatPlanFeatures($plan->features ?? []);
            $missingFeatures = $this->formatPlanFeatures(
                $this->findMissingFeatures(
                    $plan->features ?? []
                )
            );
            $periodDeclination = $this->monthsDeclination($plan->period->value);

            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => number_format($plan->price, 2, ',', ' ') . ' PLN',
                'period' => $plan->period->value . ' ' . $periodDeclination,
                'features' => $features,
                'missing_features' => $missingFeatures,
                'weight' => $plan->weight,
                'is_current' => $this->currentSubscription?->plan_id === $plan->id,
                'type' => $plan->type->label(),
            ];
        })->toArray();
    }

    protected function formatPlanFeatures(array $features): array
    {
        $formattedFeatures = [];

        foreach ($features as $feature) {
            if (is_numeric($feature)) {
                // Feature is a SystemModules enum value
                $module = SystemModules::tryFrom((int)$feature);
                if ($module) {
                    $formattedFeatures[] = $module->label();
                }
            } elseif (is_string($feature)) {
                // Feature is a custom string
                $formattedFeatures[] = $feature;
            }
        }

        return $formattedFeatures;
    }

    protected function findPlansDifferences(array $currentFeatures, array $newFeatures): array
    {
        sort($currentFeatures);
        sort($newFeatures);
        return count($currentFeatures) > count($newFeatures) ?
            array_diff($currentFeatures, $newFeatures) :
            array_diff($newFeatures, $currentFeatures);
    }

    protected function findMissingFeatures(array $currentFeatures): array
    {
        return array_diff(Arr::map(SystemModules::listed(), fn($module) => $module->value), $currentFeatures);
    }

    protected function shouldShowRenewalButton(): bool
    {
        return $this->currentSubscription &&
            $this->pendingPayment === null &&
            $this->currentSubscription->status === SubscriptionStatus::ACTIVE &&
            $this->currentSubscription->plan->type === PlanType::PAID &&
            $this->pendingSubscription === null &&
            SubRepository::isSubscriptionNearExpiry($this->currentSubscription);
    }

    protected function shouldShowCancelButton(): bool
    {
        return $this->currentSubscription &&
            $this->currentSubscription->status === SubscriptionStatus::ACTIVE &&
            $this->currentSubscription->plan->type === PlanType::PAID &&
            $this->pendingSubscription === null;
    }

    protected function shouldShowContinuePaymentButton(): bool
    {
        return $this->pendingPayment !== null;
    }

    public function createNewSubscription(): Action
    {
        return Action::make('createNewSubscription')
            ->label(function () {
                return $this->isPriceToPayVisible() ? 'Zmień plan' : 'Wybierz plan';
            })
            ->requiresConfirmation()
            ->disabled(fn () => $this->pendingPayment !== null)
            ->modalHeading(fn() => $this->getCreateHeading())
            ->modalDescription(fn() => $this->getCreateDescription())
            ->modalSubmitActionLabel(fn() => $this->getCreateSubmitLabel())
            ->modalWidth(MaxWidth::Large)
            ->fillForm(function (array $arguments) {
                $plan = Plan::findOrFail($arguments['planId']);
                $data = [
                    'description' => $plan->name . ': ' . $plan->description,
                    'price' => $plan->price,
                    'type' => $plan->type->name,
                    'price_to_pay' => $plan->price,
                    'features' => $plan->features,
                ];

                if ($this->currentSubscription) {
                    $data['actual_price'] = SubRepository::calculateUpgradePlanPrice(
                        $plan,
                        $this->currentSubscription
                    );
                    $data['price_to_pay'] = $data['actual_price']['price_to_pay'];
                }
                return $data;
            })
            ->form([
                Placeholder::make('selected_plan')
                    ->label(fn() => new HtmlString('<strong>Wybrany plan</strong>'))
                    ->content(fn(Get $get) => $get('description')),
                Placeholder::make('plan_price')
                    ->label(fn() => new HtmlString('<strong>Cena planu</strong>'))
                    ->content(fn(Get $get) => $get('price') . ' PLN'),
                Placeholder::make('price_to_pay')
                    ->label(fn() => new HtmlString('<strong>Do zapłaty</strong>'))
                    ->visible(fn() => $this->isPriceToPayVisible())
                    ->content(fn(Get $get) => $get('actual_price.price_to_pay') . ' PLN'),
                Hidden::make('price_to_pay')
                    ->visible(fn() => $this->isPriceToPayVisible()),
                Radio::make('subscription_starts_at')
                    ->label('Uruchomienie nowej subskrypcji')
                    ->visible(fn(Get $get) => $get('type') === PlanType::PAID->name)
                    ->required()
                    ->validationMessages([
                        'required' => 'Wybór daty rozpoczęcia subskrypcji jest wymagany.',
                    ])
                    ->options(fn() => $this->getStartPlanOptions())
                    ->columns(1),
                Checkbox::make('agree_on_terms')
                    ->label('Akceptuję regulamin')
                    ->required()
                    ->visible(fn(Get $get) => $get('type') === PlanType::PAID->name)
                    ->validationMessages([
                        'required' => 'Akceptacja regulaminu jest wymagana.',
                    ])
                    ->helperText(new HtmlString(
                        'Dokonując zakupu oświadczam, że akceptuję
                            <a href="/legal/regulamin" target="_blank" style="color: blue;">
                                regulamin serwisu
                            </a> i
                            <a href="/legal/regulamin-przetwarzania-danych" target="_blank" style="color: blue;">
                                politykę prywatności
                            </a>.'
                    )),
                Checkbox::make('agree_on_payment')
                    ->label('Zamawiam i płacę za subskrypcję')
                    ->required()
                    ->visible(fn(Get $get) => $get('type') === PlanType::PAID->name)
                    ->validationMessages([
                        'required' => 'Zaznaczenie opcji jest konieczne.',
                    ])
                    ->helperText('Rozumiem, że potwierdzenie zamówienia wiąże się z obowiązkiem opłacenia subskrypcji.
                    Zostaniesz przekierowany na stronę pośrednika w celu dokonania płatności.'),
                Placeholder::make('invoice_templates')
                    ->label(fn() => new HtmlString('<strong>Wzór faktury</strong>'))
                    ->visible(function (Get $get) {
                        return (tenant()?->hasModule(SystemModules::INVOICE_TEMPLATES) ?? false) &&
                            tenant()->getInvoiceConfiguration()['selected_template'] !==
                            config('app.default_invoice_template') &&
                            !in_array(SystemModules::INVOICE_TEMPLATES->value, $get('features') ?? []);
                    })
                    ->content('Wzór faktury zostanie zmieniony na domyślny.'),
            ])
            ->extraAttributes(['class' => 'w-full'])
            ->action(function (array $arguments, array $data) {
                $plan = Plan::findOrFail($arguments['planId']);
                $options = [];
                if ($data['subscription_starts_at'] === 'pending') {
                    if (!$this->currentSubscription) {
                        throw new \Exception('No current subscription');
                    }

                    $startsAt = Carbon::parse($this->currentSubscription->ends_at)->addDay();
                    $endsAt = $startsAt->clone()->addMonths($plan->period->value)->endOfDay();
                    $options['starts_at'] = $startsAt;
                    $options['ends_at'] = $endsAt;
                }
                $subRepo = new SubRepository();
                if ($this->isPriceToPayVisible()) { // upgrade
                    $price['actual_price'] = SubRepository::calculateUpgradePlanPrice(
                        $plan,
                        $this->currentSubscription
                    );
                    $options['price_to_pay'] = $price['actual_price']['price_to_pay'];
                    $subRepo->upgradeSubscriptionWithPayment(
                        auth()->user(),
                        $plan,
                        $this->currentSubscription,
                        $options
                    );
                } else {
                    $subRepo->createSubscriptionWithPayment(auth()->user(), $plan, $options);
                }

                if ($subRepo->isPaymentFinished) {
                    tenant(true);
                    $this->redirect(SubscriptionManagement::getUrl());
                    return;
                }
                $this->redirectUrl = $subRepo->continuePaymentUrl;
                $this->redirect = true;
            })
            ->after(fn() => $this->redirect ? $this->redirect($this->redirectUrl) : $this->redirect(self::getUrl()));
    }

    protected function getStartPlanOptions(): array
    {

        $options = [
            'now' => 'Plan ma zostać uruchomiony od razu po dokonaniu płatności',
        ];

        if ($this->currentSubscription && $this->currentSubscription->ends_at->isFuture()) {
            $options['now'] .= '. Aktualna subskrypcja wygaśnie.';
        }

        return $options;
    }

    protected function monthsDeclination(int $months): string
    {
        return match ($months) {
            1 => 'miesiąc',
            2 => 'miesiące',
            3 => 'miesiące',
            4 => 'miesiące',
            default => 'miesięcy',
        };
    }

    protected function getCreateHeading(): string
    {
        return blank($this->currentSubscription) ?
            'Utwórz nową subskrypcję' :
            'Ulepsz subskrypcję';
    }

    protected function getCreateDescription(): string
    {
        return blank($this->currentSubscription) ?
            'Czy na pewno chcesz utworzyć nową subskrypcję?' :
            'Czy na pewno chcesz ulepszyć swoją subskrypcję?';
    }

    protected function getCreateSubmitLabel(): string
    {
        return blank($this->currentSubscription) ?
            'Tak, utwórz' :
            'Tak, zmień subskrypcję';
    }

    protected function isPriceToPayVisible(): bool
    {
        return $this->currentSubscription && $this->currentSubscription->plan->type === PlanType::PAID;
    }

    protected function deletePayment(Payment $latestPayment): bool
    {
        $provider = $latestPayment->getPaymentProviderInstance();
        $latestPayment->subscription->forceDelete();
        if ($latestPayment->tenant->subscription()->count() === 0) {
            $latestPayment->tenant->removeSubFromConfig();
        }
        if (!$provider->cancelPayment($latestPayment->refresh())) {
            Notification::make()
                ->title('Błąd podczas anulowania płatności')
                ->body('Wystąpił błąd podczas anulowania płatności.' . $provider->error)
                ->danger()
                ->send();
            return false;
        }
        Notification::make()
            ->title('Płatność została anulowana')
            ->success()
            ->send();
        return true;
    }

    private function getSectionHeaderActions(): array
    {
        $latestPayment = $this->pendingPayment;
        $actions = [];
        if ($latestPayment && $latestPayment->getFinishPaymentLink()) {
            $actions[] = \Filament\Infolists\Components\Actions\Action::make('continuePayment')
                ->label('Kontynuuj płatność')
                ->icon('heroicon-o-credit-card')
                ->color('warning')
                ->url($latestPayment->getFinishPaymentLink())
                ->openUrlInNewTab(false);
            $actions[] = \Filament\Infolists\Components\Actions\Action::make('deletePayment')
                ->label('Anuluj płatność')
                ->icon('heroicon-o-credit-card')
                ->requiresConfirmation()
                ->color('danger')
                ->action(fn() => $this->deletePayment($latestPayment))
                ->after(fn() => $this->redirect(self::getUrl(), true));
        }
        return $actions;
    }
}
