<?php

namespace App\Filament\App\Pages;

use App\Models\Payment;
use App\Enums\PaymentStatus;
use Filament\Facades\Filament;
use Filament\Pages\Page;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Infolist;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Pages\SimplePage;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;

class PaymentThankYou extends Page
{
    use InteractsWithInfolists;

    protected static string $view = 'filament.app.pages.payment-thank-you';

    protected static bool $shouldRegisterNavigation = false;

    protected ?string $heading = '';
    protected ?string $subheading = '';

    public ?Payment $payment = null;

    public ?string $paymentStatus = null;
    public string $paymentId;
    public bool $paymentFound = false;
    public string $errorMessage = '';
    protected ?string $maxWidth = 'max-w-2xl';

    // Polling properties
    public bool $isPolling = false;
    public bool $hasPollingError = false;
    public string $pollingErrorMessage = '';
    public int $pollingAttempts = 0;
    public int $maxPollingAttempts = 10; // 4 minutes at 4-second intervals

    public static function canAccess(): bool
    {
        return Filament::auth()->check();
    }

    public function mount(string $paymentId): mixed
    {
        $this->paymentId = $paymentId;
        $this->loadPayment();

        // Start polling if payment is pending
        if ($this->paymentFound && $this->payment->status === PaymentStatus::PENDING) {
            $this->isPolling = true;
        }

        // Don't redirect in tests, only in actual web requests
        if ($this->paymentFound && $this->payment->status === PaymentStatus::COMPLETED && !app()->runningInConsole()) {
            tenant(true);
            return redirect(SubscriptionManagement::getUrl());
        }
        return true;
    }
    protected function loadPayment(): void
    {
        try {
            $this->payment = Payment::pendingForThankYou($this->paymentId, auth()->id())->first();
            if ($this->payment) {
                $this->paymentFound = true;
                $this->paymentStatus = $this->payment->status->name;
                $this->hasPollingError = false;
                $this->pollingErrorMessage = '';
                return;
            }
        } catch (ModelNotFoundException $e) {
            $this->paymentFound = false;
            $this->errorMessage = __('app.payment_thank_you.messages.payment_not_found_description');
            abort(404);
        } catch (\InvalidArgumentException) {
            $this->paymentFound = false;
            $this->errorMessage = 'Wrong argument';
            abort(404);
        }
    }

//    #[Polling(interval: '4s', except: ['paymentInfolist'])]
    public function checkPaymentStatus(): void
    {
        // Only poll if payment is found and currently pending
        if (!$this->paymentFound || !$this->isPolling) {
            return;
        }

        // Stop polling if we've exceeded max attempts
        if ($this->pollingAttempts >= $this->maxPollingAttempts) {
            $this->isPolling = false;
            $this->hasPollingError = true;
            $this->pollingErrorMessage = 'Przekroczono maksymalny czas oczekiwania na potwierdzenie płatności.';
            return;
        }

        try {
            $this->pollingAttempts++;

            // Reload payment from database
            $freshPayment = Payment::pendingForThankYou($this->paymentId, auth()->id())->first();

            if (!$freshPayment) {
                $this->isPolling = false;
                $this->hasPollingError = true;
                $this->pollingErrorMessage = 'Nie można odnaleźć płatności.';
                return;
            }

            $previousStatus = $this->paymentStatus;
//            $this->payment = $freshPayment;

            if ($previousStatus === PaymentStatus::PENDING->name && $freshPayment->status !== PaymentStatus::PENDING) {
                $this->isPolling = false;
                $this->handleStatusChange($freshPayment->status);
            }
        } catch (\Exception $e) {
            $this->hasPollingError = true;
            $this->pollingErrorMessage = 'Wystąpił błąd podczas sprawdzania statusu płatności.';

            // Stop polling after 3 consecutive errors
            if ($this->pollingAttempts % 3 === 0) {
                $this->isPolling = false;
            }
        }
    }

    protected function handleStatusChange(PaymentStatus $newStatus): void
    {
        switch ($newStatus) {
            case PaymentStatus::COMPLETED:
                tenant(true);
                $this->redirect(SubscriptionManagement::getUrl(), true);
                break;

            case PaymentStatus::FAILED:
            case PaymentStatus::ERROR:
            case PaymentStatus::CANCELED:
                // Just refresh the page to show the new status
                $this->dispatch('payment-status-changed');
                break;
        }
    }

    public function refreshPaymentStatus(): void
    {
        $this->hasPollingError = false;
        $this->pollingErrorMessage = '';
        $this->pollingAttempts = 0;

        if ($this->paymentFound && $this->payment->status === PaymentStatus::PENDING) {
            $this->isPolling = true;
        }

        $this->checkPaymentStatus();
    }

    public function getHeadingContent(): string
    {
        if (!$this->paymentFound) {
            return __('app.payment_thank_you.headings.not_found');
        }

        return match ($this->payment->status) {
            PaymentStatus::COMPLETED => __('app.payment_thank_you.headings.completed'),
            PaymentStatus::PENDING => __('app.payment_thank_you.headings.pending'),
            PaymentStatus::FAILED => __('app.payment_thank_you.headings.failed'),
            PaymentStatus::ERROR => __('app.payment_thank_you.headings.error'),
            PaymentStatus::CANCELED => __('app.payment_thank_you.headings.canceled'),
            default => 'Status płatności'
        };
    }

    public function getSubheadingContent(): ?string
    {
        if (!$this->paymentFound) {
            return __('app.payment_thank_you.subheadings.not_found');
        }

        return match ($this->payment->status) {
            PaymentStatus::COMPLETED => __('app.payment_thank_you.subheadings.completed'),
            PaymentStatus::PENDING => __('app.payment_thank_you.subheadings.pending'),
            PaymentStatus::FAILED => __('app.payment_thank_you.subheadings.failed'),
            PaymentStatus::ERROR => __('app.payment_thank_you.subheadings.error'),
            PaymentStatus::CANCELED => __('app.payment_thank_you.subheadings.canceled'),
            default => 'Sprawdź szczegóły płatności poniżej.'
        };
    }

    public function getHeaderActions(): array
    {
        if (!$this->paymentFound || $this->payment->status !== PaymentStatus::COMPLETED) {
            return [];
        }

        return [
            \Filament\Actions\Action::make('continue')
                ->label(__('app.payment_thank_you.actions.continue_to_app'))
                ->icon('heroicon-o-arrow-right')
                ->color('success')
                ->url('/app')
        ];
    }

    public function paymentInfolist(Infolist $infolist): Infolist
    {
        if (!$this->paymentFound) {
            return $infolist->schema([]);
        }

        return $infolist
            ->record($this->payment)
            ->schema([
                Section::make(__('app.payment_thank_you.sections.payment_details'))
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('amount')
                                    ->label(__('app.payment_thank_you.fields.amount'))
                                    ->formatStateUsing(
                                        fn($state) => number_format($state / 100, 2, ',', ' ') .
                                            ' ' . $this->payment->currency
                                    )
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('status')
                                    ->label(__('app.payment_thank_you.fields.status'))
                                    ->formatStateUsing(fn($state) => $this->getStatusLabel($state))
                                    ->badge()
                                    ->color(fn($state) => $this->getStatusColor($state)),
                                TextEntry::make('created_at')
                                    ->label(__('app.payment_thank_you.fields.payment_start_date'))
                                    ->dateTime('d.m.Y H:i'),
                                Grid::make(2)
                                    ->columnSpanFull()
                                    ->schema([
                                        TextEntry::make('provider_payment_id')
                                            ->label(__('app.payment_thank_you.fields.transaction_id'))
                                            ->copyable()
                                            ->hidden(fn($state) => empty($state)),
                                        TextEntry::make('paid_at')
                                            ->label(__('app.payment_thank_you.fields.payment_date'))
                                            ->dateTime('d.m.Y H:i'),
                                    ]),
                            ]),
                    ]),

                Section::make(__('app.payment_thank_you.sections.subscription_details'))
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('subscription.plan.name')
                                    ->label(__('app.payment_thank_you.fields.plan'))
                                    ->weight(FontWeight::Bold)
                                    ->hidden(fn() => !$this->payment->subscription),
                                TextEntry::make('subscription.starts_at')
                                    ->label(__('app.payment_thank_you.fields.start_date'))
                                    ->date('d.m.Y')
                                    ->hidden(fn() => !$this->payment->subscription),
                                TextEntry::make('subscription.ends_at')
                                    ->label(__('app.payment_thank_you.fields.end_date'))
                                    ->date('d.m.Y')
                                    ->hidden(fn() => !$this->payment->subscription),
                                TextEntry::make('subscription.plan.description')
                                    ->label(__('app.payment_thank_you.fields.plan_description'))
                                    ->columnSpanFull()
                                    ->hidden(
                                        fn() => !$this->payment->subscription ||
                                        empty($this->payment->subscription->plan->description)
                                    ),
                            ]),
                    ])
                    ->hidden(fn() => !$this->payment->subscription),
            ]);
    }

    protected function getStatusLabel(PaymentStatus $status): string
    {
        return match ($status) {
            PaymentStatus::COMPLETED => __('app.enums.payment_statuses.completed'),
            PaymentStatus::PENDING => __('app.enums.payment_statuses.pending'),
            PaymentStatus::FAILED => __('app.enums.payment_statuses.failed'),
            PaymentStatus::ERROR => __('app.enums.payment_statuses.error'),
            PaymentStatus::CANCELED => __('app.enums.payment_statuses.canceled'),
            default => 'Nieznany'
        };
    }

    protected function getStatusColor(PaymentStatus $status): string
    {
        return match (Str::lower($status->name)) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'error' => 'danger',
            'canceled' => 'danger',
            default => 'gray'
        };
    }

    public function getIcon(): ?string
    {
        if (!$this->paymentFound || !$this->payment) {
            return 'heroicon-o-exclamation-triangle';
        }

        return match (strtolower($this->payment->status->name)) {
            'completed' => 'heroicon-o-check-circle',
            'pending' => 'heroicon-o-clock',
            'failed' => 'heroicon-o-x-circle',
            'error' => 'heroicon-o-exclamation-triangle',
            'canceled' => 'heroicon-o-exclamation-triangle',
            default => 'heroicon-o-information-circle'
        };
    }

    public function getIconColor(): string
    {
        if (!$this->paymentFound || !$this->payment) {
            return 'danger';
        }

        return match (strtolower($this->payment->status->name)) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'error' => 'danger',
            'canceled' => 'danger',
            default => 'gray'
        };
    }
}
