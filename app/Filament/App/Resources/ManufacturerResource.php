<?php

namespace App\Filament\App\Resources;

use App\Enums\SystemModules;
use App\Filament\App\Resources\ManufacturerResource\Pages;
use App\Models\Manufacturer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class ManufacturerResource extends Resource
{
    protected static ?string $model = Manufacturer::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';


    public static function getNavigationLabel(): string
    {
        return __('app.manufacturers.navigation.label');
    }

    public static function getBreadcrumb(): string
    {
        return __('app.manufacturers.navigation.breadcrumb');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false;
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('hash')
                    ->required()
                    ->maxLength(64),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(64),
                Forms\Components\Textarea::make('address')
                    ->required()
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\TextInput::make('installation')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('app._.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('app._.phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('app._.email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_name')
                    ->label(__('app.manufacturers.list.contact_name'))
                    ->searchable(),
//                Tables\Columns\IconColumn::make('is_active')
//                    ->label(__('app._.is_active'))
//                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app._.created_at'))
                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app._.updated_at'))
                    ->dateTime('Y-m-d H:i:s', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
//                Tables\Filters\TernaryFilter::make('is_active')
//                    ->label(__('app._.is_active')),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label("")
                    ->url(fn(Model $record): string => self::getUrl('edit', ['record' => $record->hash])),
            ]);
    }

    public static function GetCreateModalForm()
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->label(__('app._.name'))
                        ->required()
                        ->columnSpanFull()
                        ->maxLength(120),
                    Forms\Components\TextInput::make('phone')
                        ->label(__('app._.phone'))
                        ->maxLength(100),
                    Forms\Components\TextInput::make('email')
                        ->label(__('app._.email'))
                        ->email()
                        ->maxLength(60),
                    Forms\Components\TextInput::make('contact_name')
                        ->label(__('app.manufacturers.create.contact_name')),
                    Forms\Components\TextInput::make('website')
                        ->url()
                        ->label(__('app._.website')),
                    Forms\Components\Textarea::make('address')
                        ->label(__('app._.address'))
                        ->maxLength(65535)
                        ->columnSpanFull()
                ])
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListManufacturers::route('/'),
            'create' => Pages\CreateManufacturer::route('/create'),
            'edit' => Pages\EditManufacturer::route('/{record}/edit'),
        ];
    }
}
