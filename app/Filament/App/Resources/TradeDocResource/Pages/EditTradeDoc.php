<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Filament\App\Resources\TradeDocResource;
use App\Models\DTOTradeDocMeta;
use App\Models\TradeDoc;
use App\Repositories\TradeDocsRepository;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

/**
 * @property \App\Models\TradeDoc $record
 */
class EditTradeDoc extends EditRecord
{
    protected static string $resource = TradeDocResource::class;


    public function mount(int|string $record): void
    {
        $this->record = $this->resolveRecord($record);

        $this->authorizeAccess();

        $meta = $this->record->getMeta();
        $this->record->buyerdata = Arr::only($meta->buyer_address, ['name', 'address', 'postcode', 'city', 'vat_id']);
        $this->fillForm();

        $this->previousUrl = url()->previous();
    }


    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return sprintf('Edytuj %s', $this->record->full_doc_number);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->modalHeading('Usuń dokument?')
                ->visible(fn($record) => TradeDocsRepository::canDocBeRemoved($record))
                ->action(function (TradeDoc $record, Action $action) {
                    if (TradeDocsRepository::deleteTradeDoc($record)) {
                        $action->success();
                        return true;
                    }
                    $action->failure();
                    return false;
                })
                ->failureNotificationTitle('Błąd kasowania: ' . TradeDocsRepository::$error)
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction()
                ->label('Lista')
                ->icon('heroicon-o-list-bullet')
                ->url(fn() => self::getResource()::getUrl('index')),
            Action::make('products')
                ->label('Pozycje dokumentu')
                ->icon('heroicon-o-queue-list')
                ->color('gray')
                ->url(fn() => self::getResource()::getUrl('add-item', ['record' => $this->record->transaction_id]))
        ];
    }

    public function form(Form $form): Form
    {
        return TradeDocResource\Forms\TradeDocForm::editForm($form);
    }

    public function getRelationManagers(): array
    {
        return [];
    }

    protected function handleRecordUpdate(Model|TradeDoc $record, array $data): Model
    {
        $meta = $data['meta'] ?? [];
        $options = $data['options'] ?? [];
        unset($data['meta'], $data['options']);
        if (($meta['options']['different_seller'] ?? false) === false) {
            $data['seller_id'] = null;
        }
        $record = parent::handleRecordUpdate($record, Arr::except($data, 'buyerdata'));
        $metaData = DTOTradeDocMeta::make($record->meta);
        $metaData->setUpBankAccount($meta['bank_account'] ?? 'brak');
        $seller = Arr::except(tenant()?->getAttributes() ?? [], ['id', 'config']);
        $buyer = Arr::except($record->buyer->getAttributes() ?? [], ['id', 'installation']);
        $metaData->setUpIssuer($seller);
        $metaData->setUpSeller($seller);
        $metaData->setUpBuyer($buyer, $data['buyerdata'] ?? []);
        $metaData->setOption('reverse_charge', $meta['options']['reverse_charge'] ?? false);
        $metaData->setOption('mpp', $meta['options']['mpp'] ?? false);
        $metaData->setOption('different_seller', $meta['options']['different_seller'] ?? false);
        $metaData->setNote($meta['note'] ?? null);
        $record->meta->fill(['meta' => $metaData->toArray()]);
        $record->meta->save();
        TradeDocsRepository::finalInvoiceProcess($record, $data['prepaidInvoices'] ?? [], 'edit');
        $this->fill($record);
        return $record;
    }
}
