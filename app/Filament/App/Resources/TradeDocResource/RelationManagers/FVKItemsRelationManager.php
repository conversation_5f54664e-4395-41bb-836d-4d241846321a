<?php

namespace App\Filament\App\Resources\TradeDocResource\RelationManagers;

use App\Enums\DocumentTypes;
use App\Enums\TradeDocDiscountTypes;
use App\Enums\TradeDocVatMethod;
use App\Enums\VatRates;
use App\Models\Products;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use App\Repositories\TradeDocsRepository;
use App\Services\CalculateTradeDoc;
use App\Services\Filters;
use App\Traits\CalculateItemsTrait;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class FVKItemsRelationManager extends RelationManager
{
    use CalculateItemsTrait;

    protected static string $relationship = 'items';
    public array $docItem = [];

    public ?Collection $sourceDocItems = null;

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('app.trade_docs.add_item.items_section_title');
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public function mount(): void
    {
        $this->fillSourceDocItems();
        parent::mount();
    }

    public function fillSourceDocItems(): void
    {
        $this->sourceDocItems = TradeDoc::find($this->getOwnerRecord()->source_id)->items()->get();
    }

    public function form(Form $form): Form
    {
        return $this->FVK($form);
    }

    public function FVK(Form $form, $operation = 'add'): Form
    {
        $schema = $this->addFVKProductsSource($form, [], $operation);
        $schema = $this->addStandardFields($form, $schema, $operation . '-fvk');
        $schema = $this->addTagsInput($form, $schema);
        return $form
            ->columns(3)
            ->statePath('docItem')
            ->schema($schema);
    }


    public function FVS(Form $form, $operation = 'add'): Form
    {
        $schema = $this->addProductsSource($form, [], $operation);
        $schema = $this->addStandardFields($form, $schema);
        $schema = $this->addTagsInput($form, $schema);
        return $form
            ->columns(3)
            ->statePath('docItem')
            ->schema($schema);
    }


    protected function addFVKProductsSource(Form $form, array $schema, $operation = 'add')
    {
        if ($operation === 'edit') {
            $schema[] = Forms\Components\Hidden::make('source_id')
                ->dehydrated(true);
            return $schema;
        }

        $select = Forms\Components\Select::make('source_id')
            ->dehydrated(true)
            ->preload()
            ->live()
            ->afterStateUpdated(function ($state, Forms\Components\Component $component) {
                if (blank($state)) {
                    return;
                }
                $item = TradeDocItem::find($state)->toArray();
                $form = $component->getLivewire()->getCachedForms()['mountedTableActionForm'];
                $item['source_id'] = $state;
                $form->fill($item);
            })
            ->options(function ($record) {
                $original = TradeDoc::find($this->getOwnerRecord()->source_id);
                $added = $this->getOwnerRecord()->items()->whereNotNull('source_id')->pluck('source_id')->toArray();
                return $original->items()->whereNotIn('uuid', $added)->get()->pluck('label', 'uuid');
            })
            ->inlineLabel(true)
            ->required()
            ->label(__('Lista produktów'));
        $schema[] = Forms\Components\Grid::make(1)
            ->schema([
                $select
            ]);

        return $schema;
    }

    protected function addProductsSource(Form $form, array $schema, $operation = 'add')
    {
        $select = Forms\Components\Select::make('product')
            ->dehydrated(false)
            ->searchable()
            ->preload()
            ->live()
            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                if (blank($state)) {
                    return;
                }
                $set('label', Products::find($state)->name);
            })
            ->options(Products::pluck('name', 'id')->toArray())
            ->inlineLabel(true)
            ->label(__('Lista produktów'));
        $schema[] = Forms\Components\Grid::make(1)
            ->schema([
                $select
            ]);

        return $schema;
    }

    protected function addStandardFields(Form $form, array $schema, $operation = 'add'): array
    {
        $default = [
            Forms\Components\Textarea::make('label')
                ->label(__('app.trade_docs.add_item.label'))
                ->required()
                ->columnSpanFull()
                ->maxLength(512),
            Forms\Components\Grid::make(4)
                ->schema([
                    Forms\Components\TextInput::make('net_unit_price')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->stripCharacters(' ')
                        ->default(0.0)
                        ->inputMode('decimal')
                        ->label(__('app.trade_docs.add_item.net_unit_price'))
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if ($state === '0') {
                                    return;
                                }
                                $this->docItem['net_unit_price'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->visible(fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET)
                        ->dehydratedWhenHidden(true)
                        ->required(),
                    Forms\Components\TextInput::make('gross_unit_price')
                        ->default(0.0)
                        ->required()
                        ->stripCharacters(' ')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                $this->validateOnly('gross_unit_price');
                                if (blank($state)) {
                                    $component->state(0);
                                }
                                $this->docItem['gross_unit_price'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->dehydratedWhenHidden(true)
                        ->visible(fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS)
                        ->label(__('app.trade_docs.add_item.gross_unit_price')),
                    Forms\Components\Select::make('discount_type')
                        ->label(__('app.trade_docs.add_item.discount_type'))
                        ->options(TradeDocDiscountTypes::toArrayWithLabels())
                        ->live()
                        ->selectablePlaceholder(false)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if ((int)$state === 0) {
                                    $set('discount_value', 0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->required(),
                    Forms\Components\TextInput::make('discount_value')
                        ->disabled(fn(Forms\Get $get) => (int)$get('discount_type') === 0)
                        ->minValue(0)
                        ->stripCharacters(' ')
                        ->extraAlpineAttributes(
                            Filters::getFilter(['price' => 'keypress.self'])
                        )
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                if (null === $state) {
                                    $set('discount_value', 0);
                                    return;
                                }
                                $this->docItem['discount_value'] = Str::of($state)->replace(',', '.')->toFloat();
                                $this->calculate($set);
                            }
                        )
                        ->default(0)
                        ->label(__('app.trade_docs.add_item.discount_value')),
                    Forms\Components\TextInput::make('discounted_unit_price')
                        ->default(0.0)
                        ->disabled()
                        ->dehydrated(true)
                        ->label(__('app.trade_docs.add_item.discounted_unit_price')),
                ]),
            Forms\Components\Grid::make(6)
                ->schema([
                    Forms\Components\TextInput::make('amount')
                        ->default(1)
                        ->required()
                        ->live(onBlur: true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state, Forms\Components\Component $component) {
                                if ($state === null) {
                                    $component->state(0);
                                }
                                $this->calculate($set);
                            }
                        )
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.amount')),
                    Forms\Components\TextInput::make('unit_type')
                        ->default('szt.')
                        ->label(__('app.trade_docs.add_item.unit_type')),
                    Forms\Components\TextInput::make('net_value')
                        ->default(0.0)
                        ->disabled()
                        ->dehydrated(true)
                        ->label(__('app.trade_docs.add_item.net_value')),
                    Forms\Components\Select::make('vat_label')
                        ->options(fn() => VatRates::getRatesForSelect())
                        ->live(condition: blank($record->source_id ?? null))
                        ->selectablePlaceholder(false)
                        ->disabled($operation === 'add-fvk' || $operation === 'edit-fvk')
                        ->dehydratedWhenHidden(true)
                        ->dehydrated(true)
                        ->afterStateUpdated(
                            function (Forms\Set $set, $state) {
                                $set('vat_rate', VatRates::getRate($state));
                                $this->calculate($set);
                            }
                        )
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_label')),
                    Forms\Components\TextInput::make('vat_rate')
                        ->hidden()
                        ->dehydratedWhenHidden(true)
                        ->numeric()
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_rate')),
                    Forms\Components\TextInput::make('vat_value')
                        ->numeric()
                        ->default(0.0)
                        ->disabled()
                        ->dehydrated(true)
                        ->mask(
                            RawJs::make('$money($input, \'.\', \'\')')
                        )
                        ->label(__('app.trade_docs.add_item.vat_value')),
                    Forms\Components\TextInput::make('gross_value')
                        ->numeric()
                        ->disabled()
                        ->dehydrated(true)
                        ->default(0.0)
                        ->mask(
                            RawJs::make('$money($input, \'.\', \'\')')
                        )
                        ->label(__('app.trade_docs.add_item.gross_value')),
                ])
        ];
        return array_merge($schema, $default);
    }

    protected function addTagsInput(Form $form, array $schema): array
    {
        $tags = Forms\Components\TagsInput::make('tags')
            ->live()
            ->afterStateUpdated(function ($state, $set) {
                $modifiedTags = array_map(function ($tag) {
                    return strtolower(trim($tag));
                }, $state);

                $set('tags', $modifiedTags);
            })
            ->suggestions([])
            ->label(__('app.trade_docs.add_item.tags'));
        return array_merge($schema, [$tags]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('label')
            ->columns([
                Tables\Columns\TextColumn::make('source_id')
                    ->label('Korekta')
                    ->default('')
                    ->formatStateUsing(
                        function ($state) {
                            return new HtmlString('Przed<br>Po<br><small>Różnica</small>');
                        }
                    ),

                Tables\Columns\TextColumn::make('label')
                    ->wrap(true)
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'label');
                    })
                    ->label(__('app.trade_docs.add_item.label')),

                Tables\Columns\TextColumn::make('net_unit_price')
                    ->label(__('app.trade_docs.add_item.net_unit_price'))
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'net_unit_price');
                    })
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET
                    ),

                Tables\Columns\TextColumn::make('gross_unit_price')
                    ->label(__('app.trade_docs.add_item.gross_unit_price'))
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'gross_unit_price');
                    })
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS
                    ),

                Tables\Columns\TextColumn::make('discount_value')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'discount_value');
                    })
                    ->label(__('app.trade_docs.add_item.discount_value')),

                Tables\Columns\TextColumn::make('discounted_unit_price')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'discounted_unit_price');
                    })
                    ->label(__('app.trade_docs.add_item.discounted_unit_price')),

                Tables\Columns\TextColumn::make('amount')
                    ->label(__('app.trade_docs.add_item.amount'))
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues(
                            $record,
                            $state,
                            'amount'
                        );
                    }),

                Tables\Columns\TextColumn::make('unit_type')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'unit_type');
                    })
                    ->label(__('app.trade_docs.add_item.unit_type')),

                Tables\Columns\TextColumn::make('vat_label')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'vat_label');
                    })
                    ->hidden(fn() => $this->getOwnerRecord()->type === DocumentTypes::FAUP)
                    ->label(__('app.trade_docs.add_item.vat_label')),

                Tables\Columns\TextColumn::make('net_value')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'net_value');
                    })
                    ->label(__('app.trade_docs.add_item.net_value'))
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_NET
                    ),

                Tables\Columns\TextColumn::make('gross_value')
                    ->formatStateUsing(function ($state, $record) {
                        return $this->generateColumnValues($record, $state, 'gross_value');
                    })
                    ->label(__('app.trade_docs.add_item.gross_value'))
                    ->visible(
                        fn() => $this->getOwnerRecord()->vat_method === TradeDocVatMethod::BASE_ON_GROSS
                    ),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Dodaj pozycję')
                    ->modalHeading('Dodaj pozycję do dokumentu')
                    ->modalDescription('Dodaj lub edytuj pozycję')
                    ->form(fn() => $this->FVS(self::makeForm()))
                    ->using(function (array $data, HasTable $livewire) {
                        return TradeDocsRepository::createDocItem($this->getOwnerRecord(), $data);
                    })
                    ->after(fn() => $this->dispatch('itemCreated')),
                Tables\Actions\Action::make('add-corrected-item')
                    ->label('Dodaj korygowany produkt')
                    ->modalHeading('Dodaj korygowany produkt')
                    ->modalDescription('Edytuj produkt')
                    ->form(fn() => $this->FVK(self::makeForm()))
                    ->action(function (array $data, HasTable $livewire) {
                        return TradeDocsRepository::createFVKDocItem($this->getOwnerRecord(), $data);
                    })
                    ->after(fn() => $this->dispatch('itemCreated')),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edytuj produkt')
                    ->modalDescription('Dodaj lub edytuj produkt')
                    ->mutateRecordDataUsing(function ($data) {
                        $item = $this->getOwnerRecord()->getMeta()->getItem($data['uuid']);
                        if (blank($item)) {
                            return $data;
                        }
                        return array_merge($data, $item);
                    })
                    ->form(
                        function ($record) {
                            return match (filled($record->source_id)) {
                                true => $this->FVK(self::makeForm(), 'edit'),
                                default => $this->FVS(self::makeForm(), 'edit')
                            };
                        }
                    )
                    ->using(function (array $data, TradeDocItem $record) {
                        $docRecord = $this->getOwnerRecord();
                        TradeDocsRepository::updateFVKDocItem($docRecord, $record, $data);
                        $this->dispatch('itemUpdated');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->using(function (TradeDocItem $record) {
                        $record->delete();
                        $meta = $this->getOwnerRecord()->getMeta();
                        $meta->removeItem($record->uuid);
                        $this->getOwnerRecord()->meta->update(['meta' => $meta]);
                    })
                    ->after(function (TradeDocItem $record) {
                        $docRecord = $this->getOwnerRecord();
                        TradeDocsRepository::updateDocSummary($docRecord);
                        $this->dispatch('itemRemoved');
                    }),
            ])
            ->searchable(false)
            ->emptyStateHeading('Nie znaleziono pozycji dokumentu')
            ->emptyStateDescription('Dodaj pozycje do dokumentu')
            ->bulkActions([]);
    }

    public function generateColumnValues(
        TradeDocItem $record,
        $state,
        string $fieldName,
        $default = '-'
    ): HtmlString|string {
        if ($record->source_id === null) {
            return new HtmlString($default . '<br>' . $state . '<br><small>' . $default . '</small>');
        }

        $orig = $this->sourceDocItems->firstWhere('uuid', $record->source_id);
        $item = $this->getOwnerRecord()->getMeta()->getItem($record->uuid);
        if (blank($item)) {
            return new HtmlString($default . '<br>' . $state . '<br><small>' . $default . '</small>');
        }

        $diffValue = $state;
        $originalValue = $orig->{$fieldName};
        $changedValue = $item[$fieldName] ?? $state;
//        $changedValue = $state;

        if ($fieldName === 'amount') {
            $originalValue = number_format($originalValue, 2, '.', '');
            $changedValue = number_format($changedValue, 2, '.', '');
            $diffValue = number_format($diffValue, 2, '.', '');
        }
        return new HtmlString($originalValue . '<br>' . $changedValue . '<br><small>' . $diffValue . '</small>');
    }

    public function getUnitPrice(TradeDocItem $record): string
    {
        return match ($this->getOwnerRecord()->vat_method) {
            TradeDocVatMethod::BASE_ON_NET => $record->net_unit_price,
            default => $record->gross_unit_price,
        };
    }

    public function getFilter(string $key): array
    {
        return match ($key) {
            'net_unit_price' =>
            [
                'x-on:keypress.self' => '(event) => !filterInput.price(event) && event.preventDefault()',
            ],
            default => []
        };
    }
}
