<?php

namespace App\Filament\App\Resources;

use App\Enums\SystemModules;
use App\Filament\App\Resources\WarehouseDocResource\Pages;
use App\Filament\App\Resources\WarehouseDocResource\RelationManagers;
use App\Models\WarehouseDoc;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WarehouseDocResource extends Resource
{
    protected static ?string $model = WarehouseDoc::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';


    protected static ?string $modelLabel = 'dokument';
    protected static ?string $pluralModelLabel = 'Dokumenty';
    protected static bool $hasTitleCaseModelLabel = false;

    protected static ?string $navigationLabel = 'Dokumenty';


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }

    public static function getNavigationGroup(): ?string
    {
//        if (auth()->user()->isTenantAdmin()) {
//            return 'Magazyn';
//        }
        return null;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWarehouseDocs::route('/'),
            'create' => Pages\CreateWarehouseDoc::route('/create'),
            'edit' => Pages\EditWarehouseDoc::route('/{record}/edit'),
            'add-item' => Pages\AddDocItem::route('/{record}/add-item'),
        ];
    }
}
