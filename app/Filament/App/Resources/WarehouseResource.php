<?php

namespace App\Filament\App\Resources;

use App\Enums\SystemModules;
use App\Enums\WarehouseTypes;
use App\Filament\App\Resources\WarehouseResource\Pages;
use App\Filament\App\Resources\WarehouseResource\RelationManagers;
use App\Models\Warehouse;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class WarehouseResource extends Resource
{
    protected static ?string $model = Warehouse::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return __('app.warehouses.navigation.label');
    }

    public static function getBreadcrumb(): string
    {
        return __('app.warehouses.navigation.breadcrumb');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return tenant()?->hasModule(SystemModules::WAREHOUSE) ?? false;
    }

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::WAREHOUSE);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('app.warehouses.list.Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('ownertype')
                    ->label(__('app.warehouses.list.Owner type'))
                    ->state(function (Warehouse $record) {
                        return __($record->owner_type->name);
                    }),
                Tables\Columns\TextColumn::make('owner_identifier')
                    ->state(function (Warehouse $record) {
                        return match ($record->owner_type) {
                            WarehouseTypes::COMPANY => $record->owner->name,
                            default => $record->owner->email
                        };
                    })
                    ->label(__('app.warehouses.list.Owner'))
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('app.warehouses.list.Is active'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                    ->label(__('app.warehouses.list.Created at'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app.warehouses.list.Updated at'))
                    ->dateTime('Y-m-d', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->url(fn(Model $record): string => self::getUrl('edit', ['record' => $record->hash])),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getModelLabel(): string
    {
        return __('app.warehouses.edit.page.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('app.warehouses.edit.page.plural');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWarehouses::route('/'),
            'create' => Pages\CreateWarehouse::route('/create'),
            'edit' => Pages\EditWarehouse::route('/{record}/edit'),
        ];
    }
}
