<?php

namespace App\Filament\App\Resources;

use App\Enums\SystemModules;
use App\Filament\App\Resources\PurchaseDocResource\Pages;
use App\Filament\App\Resources\PurchaseDocResource\RelationManagers;
use App\Models\PurchaseDoc;
use App\Repositories\DocumentSeriesRepository;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class PurchaseDocResource extends Resource
{
    protected static ?string $model = PurchaseDoc::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function canAccess(): bool
    {
        return parent::canAccess() && tenant()->hasModule(SystemModules::PURCHASE_INVOICES);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('seller.name')
                    ->label(__('app.purchase_docs.list.seller'))
                    ->formatStateUsing(fn($record, $state) => $record->seller->short_name ?? $state)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('seller.vat_id')
                    ->label(__('app.purchase_docs.list.vat_id'))
                    ->searchable()
                    ->visibleFrom('md')
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('app.purchase_docs.list.type'))
                    ->formatStateUsing(fn($state) => $state->label())
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('full_doc_number')
                    ->label(__('app.purchase_docs.list.doc_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label(__('app.purchase_docs.list.payment_date'))
                    ->date('Y-m-d')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('issued_at')
                    ->label(__('app.purchase_docs.list.issued_at'))
                    ->date('Y-m-d')
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency')
                    ->label(__('app.purchase_docs.list.currency'))
                    ->state(
                        fn(PurchaseDoc $record) => $record->currency !== 'PLN' && self::currencyDisplayFilter() ?
                            $record->currency . '/PLN' :
                            $record->currency
                    )
                    ->visibleFrom('md')
                    ->searchable(),
                Tables\Columns\TextColumn::make('net')
                    ->label(__('app.purchase_docs.list.net'))
                    ->state(
                        fn(PurchaseDoc $record) => $record->netValue(
                            self::currencyDisplayFilter()
                        )
                    )
                    ->summarize(
                        Tables\Columns\Summarizers\Summarizer::make('netto')
                            ->using(
                                function (\Illuminate\Database\Query\Builder $query, Table $table) {
                                    return $query->when(
                                        $table
                                            ->getFilter('src_currency')
                                            ?->getState()['show_in_base_currency'] ?? false,
                                        fn($query) => $query->sum(DB::raw('net / exchange_rate')),
                                        fn($query) => $query->sum('net'),
                                    );
                                }
                            )
                            ->money(currency: fn(Table $table) => $table
                                ->getFilter('src_currency')
                                ?->getState()['show_in_base_currency'] ?? false ? 'PLN' : '', divideBy: 100)
                    )
                    ->numeric(2)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('vat_amount')
                    ->state(
                        fn(PurchaseDoc $record) => $record->vatValue(
                            self::currencyDisplayFilter()
                        )
                    )
                    ->summarize(
                        Tables\Columns\Summarizers\Summarizer::make('vatAmount')
                            ->using(
                                function (Builder $query, Table $table) {
                                    return $query->when(
                                        $table
                                            ->getFilter('src_currency')
                                            ?->getState()['show_in_base_currency'] ?? false,
                                        fn($query) => $query->sum(DB::raw('vat_amount / exchange_rate')),
                                        fn($query) => $query->sum('vat_amount'),
                                    );
                                }
                            )
                            ->money(currency: fn(Table $table) => $table
                                ->getFilter('src_currency')
                                ?->getState()['show_in_base_currency'] ?? false ? 'PLN' : '', divideBy: 100)
                    )
                    ->label(__('app.trade_docs.list.vat_amount'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric(2)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('gross')
                    ->label(__('app.trade_docs.list.gross'))
                    ->state(
                        fn(PurchaseDoc $record) => $record->grossValue(
                            self::currencyDisplayFilter()
                        )
                    )
                    ->summarize(
                        Tables\Columns\Summarizers\Summarizer::make('brutto')
                            ->using(
                                function (\Illuminate\Database\Query\Builder $query, Table $table) {
                                    return $query->when(
                                        $table
                                            ->getFilter('src_currency')
                                            ?->getState()['show_in_base_currency'] ?? false,
                                        fn($query) => $query->sum(DB::raw('gross / exchange_rate')),
                                        fn($query) => $query->sum('gross'),
                                    );
                                }
                            )
                            ->money(currency: fn(Table $table) => $table
                                ->getFilter('src_currency')
                                ?->getState()['show_in_base_currency'] ?? false ? 'PLN' : '', divideBy: 100)
                    )
                    ->numeric(2)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_paid')
                    ->alignCenter()
                    ->action(
                        PurchaseDocResource\Forms\PurchaseDocForm::getTablePaymentAction()
                    )
                    ->label(__('app.purchase_docs.list.is_paid'))
                    ->tooltip(
                        fn($state, $record) => $state ? 'Opłacono ' . $record->payment_date->format('Y-m-d') : 'Opłać'
                    )
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_accepted')
                    ->label(__('app.purchase_docs.list.is_accepted'))
                    ->alignCenter()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->boolean(),
                Tables\Columns\IconColumn::make('has_correction')
                    ->alignCenter()
                    ->label(__('app.purchase_docs.list.has_correction'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('app.purchase_docs.list.created_at'))
                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('app.purchase_docs.list.updated_at'))
                    ->dateTime('Y-m-d H:i', 'Europe/Warsaw')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_paid')
                    ->label(__('app.trade_docs.list.filters.is_paid_label')),
                Tables\Filters\SelectFilter::make('type')
                    ->label(__('app.trade_docs.list.filters.doctype_label'))
                    ->options(
                        DocumentSeriesRepository::getPDocForCreateSelect()
                    ),
                Tables\Filters\Filter::make('src_currency')
                    ->form([
                        Forms\Components\Checkbox::make('show_in_base_currency')
                            ->label('Przelicz na PLN')
                            ->default(true)
                            ->inline(false)
                            ->inlineLabel(false)
                    ])
                    ->indicateUsing(function ($data) {
                        return match ($data['show_in_base_currency']) {
                            true => 'Przelicz na PLN',
                            default => 'Nie przeliczaj waluty',
                        };
                    }),
                Tables\Filters\Filter::make('period')
                    ->form([
                        Forms\Components\Select::make('period')
                            ->label('Okres')
                            ->key('periodList')
                            ->live()
                            ->afterStateUpdated(function (Pages\ListPurchaseDocs $livewire, $state) {
                                if ($state !== 'dates') {
                                    $table = $livewire->getTable();
                                    $table
                                        ->getFilter('issued_at_dates')
                                        ?->resetState([
                                            "issued_at_from" => null,
                                            "issued_at_to" => null
                                        ])->getForm()->fill();
                                }
                            })
                            ->options([
                                'thisWeek' => 'Ten tydzień',
                                'lastWeek' => 'Ostatni tydzień',
                                'thisMonth' => 'Ten miesiąc',
                                'lastMonth' => 'Ostatni miesiąc',
                                'thisYear' => 'Ten rok',
                                'lastYear' => 'Ostatni rok',
                                'dates' => 'Daty',
                            ])
                    ])
                    ->query(function ($query, $data) {
                        if (blank($data['period']) || $data['period'] === 'dates') {
                            return $query;
                        }

                        switch ($data['period']) {
                            case 'thisWeek':
                                $query->where('issued_at', '>=', now()->startOfWeek());
                                $query->where('issued_at', '<=', now()->endOfWeek());
                                break;
                            case 'lastWeek':
                                $query->where('issued_at', '>=', now()->subWeek()->startOfWeek());
                                $query->where('issued_at', '<=', now()->subWeek()->endOfWeek());
                                break;
                            case 'thisMonth':
                                $query->where('issued_at', '>=', now()->startOfMonth());
                                $query->where('issued_at', '<=', now()->endOfMonth());
                                break;
                            case 'lastMonth':
                                $query->where('issued_at', '>=', now()->subMonth()->startOfMonth());
                                $query->where('issued_at', '<=', now()->subMonth()->endOfMonth());
                                break;
                            case 'thisYear':
                                $query->where('issued_at', '>=', now()->startOfYear());
                                $query->where('issued_at', '<=', now()->endOfYear());
                                break;
                            case 'lastYear':
                                $query->where('issued_at', '>=', now()->subYear()->startOfYear());
                                $query->where('issued_at', '<=', now()->subYear()->endOfYear());
                                break;
                        }

                        return $query;
                    })
                    ->indicateUsing(function ($data, $table): array {
                        $indicators = [];
                        if (blank($data['period'])) {
                            return $indicators;
                        }
                        $label = $table
                            ->getFilter('period')
                            ->getForm()
                            ->getComponent('periodList')
                            ->getOptions()[$data['period']] ?? 'unknown';
                        if (filled($data['period'])) {
                            $indicators[] = Indicator::make('Okres: ' . $label)
                                ->removeField('period');
                        }
                        return $indicators;
                    }),
                Tables\Filters\Filter::make('issued_at_dates')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('issued_at_from')
                                    ->displayFormat('Y-m-d')
                                    ->visible(
                                        fn() => ($table->getFilter('period')?->getState()['period'] ?? null) === 'dates'
                                    )
                                    ->label('Wystawiona od')
                                    ->format('Y-m-d')
                                    ->native(false),
                                Forms\Components\DatePicker::make('issued_at_to')
                                    ->displayFormat('Y-m-d')
                                    ->visible(
                                        fn() => ($table->getFilter('period')?->getState()['period'] ?? null) === 'dates'
                                    )
                                    ->label('Wystawiona do')
                                    ->format('Y-m-d')
                                    ->native(false),
                            ])
                    ])
                    ->columnSpan(2)
                    ->query(function ($query, $data) {
                        if (blank($data['issued_at_from']) && blank($data['issued_at_to'])) {
                            return $query;
                        }

                        if (filled($data['issued_at_from'])) {
                            $query->where('issued_at', '>=', Carbon::make($data['issued_at_from'])->startOfDay());
                        }

                        if (filled($data['issued_at_to'])) {
                            $query->where('issued_at', '<=', Carbon::make($data['issued_at_to'])->endOfDay());
                        }
                        return $query;
                    })
                    ->indicateUsing(function ($data, PurchaseDocResource\Pages\ListPurchaseDocs $livewire): array {

                        $indicators = [];
                        if (blank($data['issued_at_from']) && blank($data['issued_at_to'])) {
                            return $indicators;
                        }
                        $table = $livewire->getTable();

                        if (($table->getFilter('period')?->getState()['period'] ?? null) !== 'dates') {
                            return $indicators;
                        }

                        $issued_at_from = $data['issued_at_from'];
                        $issued_at_to = $data['issued_at_to'];
                        if (filled($issued_at_from)) {
                            $indicators[] = Indicator::make('Od ' . Carbon::make($issued_at_from)->format('Y-m-d'))
                                ->removeField('issued_at_from');
                        }
                        if (filled($issued_at_to)) {
                            $indicators[] = Indicator::make('Do ' . Carbon::make($issued_at_to)->format('Y-m-d'))
                                ->removeField('issued_at_to');
                        }
                        return $indicators;
                    })
            ])
            ->filtersLayout(Tables\Enums\FiltersLayout::AboveContentCollapsible)
            ->persistFiltersInSession(true)
            ->filtersFormColumns(6)
            ->actions([
                Tables\Actions\Action::make('additem')
                    ->url(fn(PurchaseDoc $record) => self::getUrl('add-item', ['record' => $record->getKey()]))
                    ->label('Dodaj pozycję')
                    ->hiddenLabel()
                    ->color('regentGray-500')
                    ->iconButton()
                    ->icon('heroicon-o-queue-list'),
                Tables\Actions\Action::make('print_doc')
                    ->label('PDF')
                    ->iconButton()
                    ->icon('heroicon-o-printer')
                    ->url(
                        fn(PurchaseDoc $record) => route('print', [
                            'doctype' => strtolower($record->type->getGeneralType()->value),
                            'doc' => $record->getKey(),
                            'output' => 'pdf'
                        ]),
                        true
                    ),
            ])
            ->recordAction('triggerAction')
            ->recordUrl(null)
            ->defaultSort('issued_at', 'desc')
            ->defaultPaginationPageOption(25)
            ->bulkActions([]);
    }

    public static function currencyDisplayFilter(bool $default = false): bool
    {
        return self::getFiltersSessionState('src_currency.show_in_base_currency', $default);
    }

    public static function getFiltersSessionState($filter, $default): mixed
    {
        return Session::get('tables.ListPurchaseDocs_filters.' . $filter, $default);
    }

    public static function getRelations(): array
    {
        return [
//            RelationManagers\ItemsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPurchaseDocs::route('/'),
            'create' => Pages\CreatePurchaseDoc::route('/create'),
            'edit' => Pages\EditPurchaseDoc::route('/{record}/edit'),
            'add-item' => Pages\AddPurchaseDocItem::route('/{record}/add-item'),
        ];
    }
}
