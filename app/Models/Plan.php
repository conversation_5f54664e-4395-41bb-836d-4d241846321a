<?php

namespace App\Models;

use App\Enums\MoneyVOCast;
use App\Enums\PlanPeriod;
use App\Enums\PlanType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id Primary key (unsigned bigint)
 * @property string $name Plan name (varchar 255)
 * @property string $description Plan description (text)
 * @property int $price Plan price in smallest currency unit (unsigned int)
 * @property PlanPeriod $period Duration of the plan in months (unsigned int)
 * @property PlanType $type Type of the plan (varchar 64)
 * @property array|null $features JSON-encoded features list (nullable longtext)
 * @property bool $is_active Whether the plan is active (tinyint 1, default 1)
 * @property int $weight Weight of the plan
 * @property \Carbon\Carbon|null $created_at Timestamp of creation (nullable)
 * @property \Carbon\Carbon|null $updated_at Timestamp of last update (nullable)
 * @property \Carbon\Carbon|null $deleted_at Timestamp of soft deletion (nullable)
 */
class Plan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'period',
        'features',
        'type',
        'is_active',
        'weight',
    ];

    protected $casts = [
        'features' => 'array',
        'is_active' => 'boolean',
        'period' => PlanPeriod::class,
        'type' => PlanType::class,
        'price' => MoneyVOCast::class,
    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function getPlanCode(): string
    {
        return 'P' . $this->id . ($this->type === PlanType::TRIAL ? 'TR' : 'PA') . $this->period->value . 'M';
    }

    public function getPriceInCents(): int
    {
        return $this->price * 100;
    }
}
