<?php

namespace App\Models;

use App\Enums\LegalDocumentStatus;
use App\Enums\LegalDocumentType;
use App\Scopes\Installation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property string $title
 * @property string $content
 * @property LegalDocumentStatus $status
 * @property int $version_number
 * @property \DateTime|null $publication_date
 * @property LegalDocumentType $document_type
 * @property string|null $html_file_path
 * @property string|null $pdf_file_path
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 */
class LegalDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'status',
        'version_number',
        'publication_date',
        'document_type',
        'html_file_path',
        'pdf_file_path',
    ];

    protected $casts = [
        'status' => LegalDocumentStatus::class,
        'document_type' => LegalDocumentType::class,
        'publication_date' => 'datetime',
    ];

//    protected static function booted()
//    {
//        if (false === auth()->user()?->isSuperAdmin()) {
//            static::addGlobalScope(new Installation());
//        }
//    }

//    public function tenant(): BelongsTo
//    {
//        return $this->belongsTo(Tenant::class, 'installation');
//    }

    // Scopes
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', LegalDocumentStatus::PUBLISHED);
    }

    public function scopeDraft(Builder $query): Builder
    {
        return $query->where('status', LegalDocumentStatus::DRAFT);
    }

    public function scopeArchived(Builder $query): Builder
    {
        return $query->where('status', LegalDocumentStatus::ARCHIVED);
    }

    public function scopeOfType(Builder $query, LegalDocumentType $type): Builder
    {
        return $query->where('document_type', $type);
    }

    // Helper methods
    public function isEditable(): bool
    {
        return $this->status === LegalDocumentStatus::DRAFT;
    }

    public function isPublished(): bool
    {
        return $this->status === LegalDocumentStatus::PUBLISHED;
    }

    public function isArchived(): bool
    {
        return $this->status === LegalDocumentStatus::ARCHIVED;
    }

    public function canBePublished(): bool
    {
        return $this->status === LegalDocumentStatus::DRAFT && !empty($this->content);
    }

    public function getFileBaseName(): string
    {
        return sprintf(
            '%s_v%d_%s',
            $this->document_type->value,
            $this->version_number,
            $this->publication_date?->format('Y-m-d') ?? now()->format('Y-m-d')
        );
    }

    public function getHtmlUrl(): ?string
    {
        return $this->html_file_path ? Storage::disk('public')->url($this->html_file_path) : null;
    }

    public function getPdfUrl(): ?string
    {
        return $this->pdf_file_path ? Storage::disk('public')->url($this->pdf_file_path) : null;
    }

    public function deleteFiles(): void
    {
        if ($this->html_file_path && Storage::disk('public')->exists($this->html_file_path)) {
            Storage::disk('public')->delete($this->html_file_path);
        }

        if ($this->pdf_file_path && Storage::disk('public')->exists($this->pdf_file_path)) {
            Storage::disk('public')->delete($this->pdf_file_path);
        }
    }
}
