<?php

namespace App\Contracts;

use App\Models\Payment;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;

interface PaymentProviderContract
{
    /**
     * Tworzy nową subskrypcję u dostawcy.
     */
    public function createSubscription(User $user, Plan $plan, array $options = []): mixed;

    /**
     * Anuluje aktywną subskrypcję u dostawcy.
     */
    public function cancelSubscription(Subscription $subscription): bool;

    /**
     * Pobiera status subskrypcji zewnętrznej.
     */
    public function getSubscriptionStatus(Subscription $subscription): string;

    /**
     * Tworzy jednorazową płatność (np. dla zakupu planu bez subskrypcji).
     */
    public function charge(User $user, Subscription $subscription, array $options = []): mixed;

    /**
     * Anuluje rozpoczętą płatność.
     */
    public function cancelPayment(Payment $payment, array $options = []): bool;

    /**
     * Obsługuje webhook z płatnościami (np. Stripe Webhook).
     */
    public function handleWebhook(Request $request): void;

    /**
     * Zwraca nazwę providera, np. "stripe", "payu".
     */
    public function getName(): string;

    public function getContinuePaymentLink(Payment $payment): string;
}
