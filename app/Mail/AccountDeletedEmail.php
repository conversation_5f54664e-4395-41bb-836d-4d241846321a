<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class AccountDeletedEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $deletedAt;

    /**
     * Create a new message instance.
     */
    public function __construct(public User $user)
    {
        $raw = DB::select("SELECT * FROM users WHERE id = " . $user->id);
        $this->deletedAt = count($raw) > 0 ? Carbon::parse($raw[0]?->deleted_at) : now();
        $this->onQueue('emails');
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Twoje konto zostało usunięte z systemu TwojeFaktury.eu',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.account-deleted',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
