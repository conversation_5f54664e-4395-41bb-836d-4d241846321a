<?php

namespace App\Mail;

use App\Models\TradeDoc;
use App\Repositories\TenantRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentReminderEmail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public TradeDoc $tradeDoc
    ) {
        $this->onQueue('emails');
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $daysUntilDue = $this->tradeDoc->getDaysUntilDue();
        $daysText = $daysUntilDue === 1 ? 'jutro' : "za {$daysUntilDue} dni";

        return new Envelope(
            subject: "Przypomnienie o płatności - faktura {$this->tradeDoc->full_doc_number} - termin {$daysText}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.payment-reminder',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
