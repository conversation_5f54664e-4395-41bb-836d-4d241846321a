<?php

namespace App\Enums;

use Illuminate\Support\Str;

enum PlanType
{
    use EnumHelper;

    case TRIAL;
    case PAID;

    public function label()
    {
        return __('app.enums.plan_types.' . $this->name);
    }

    public static function toArrayWithLabels(): array
    {
        $source = [];
        array_map(static function ($idx) use (&$source) {
            $source[$idx->name] = $idx->label();
        }, self::cases());
        return $source;
    }
}
