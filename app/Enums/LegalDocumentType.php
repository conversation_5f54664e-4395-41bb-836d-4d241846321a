<?php

namespace App\Enums;

enum LegalDocumentType: string
{
    case TOS = 'tos';
    case GDPR = 'gdpr';
    case TODP = 'todp';

    public function label(): string
    {
        return match ($this) {
            self::TOS => 'Regulamin',
            self::GDPR => 'Polityka prywatności',
            self::TODP => 'Regulamin przetwarzania danych osobowych',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::TOS => 'Regulamin określający zasady i regulacje korzystania z usługi',
            self::GDPR => 'Dokument zgodności z RODO określający zasady ochrony danych i prywatności',
            self::TODP => 'Regulamin przetwarzania danych osobowych',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->value => $case->label()])
            ->toArray();
    }

    public function color(): string
    {
        return match ($this) {
            self::TOS => 'blue',
            self::GDPR => 'green',
            self::TODP => 'pink',
        };
    }

    public function slug(): string
    {
        return match ($this) {
            self::GDPR => 'polityka-prywatnosci',
            self::TODP => 'regulamin-przetwarzania-danych',
            default => 'regulamin',
        };
    }

    public static function getIndexedBySlug(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->slug() => $case])
            ->toArray();
    }
}
