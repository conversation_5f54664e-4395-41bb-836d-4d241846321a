<?php

namespace App\Listeners;

use App\Facades\Stats;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class AccountDeletedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Stats::increment('account.deleted');
        Mail::to($event->user->email)->queue(new \App\Mail\AccountDeletedEmail($event->user));
    }
}
