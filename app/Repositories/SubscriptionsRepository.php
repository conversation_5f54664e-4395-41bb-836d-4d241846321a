<?php

namespace App\Repositories;

use App\Enums\PlanType;
use App\Enums\SubscriptionStatus;
use App\Events\SubscriptionCanceled;
use App\Facades\Stats;
use App\Filament\App\Pages\SubscriptionManagement;
use App\Mail\SubscriptionEnded;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Services\Payments\InternalPaymentProvider;
use App\Services\Payments\PayUPaymentProvider;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class SubscriptionsRepository
{

    public Subscription $subscription;
    public Plan $plan;
    public bool $isPaymentFinished = false;
    public ?string $continuePaymentUrl = null;


    public static function getActiveSubscription(User $user): ?Subscription
    {
        return Subscription::where('user_id', $user->id)
            ->where('tenant_id', $user->installation())
            ->where('status', SubscriptionStatus::ACTIVE->name)
            ->first();
    }

    public function createSubscription(User $user, Plan $plan, array $options = []): Subscription
    {
        return Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'plan_id' => $plan->id,
            'status' => SubscriptionStatus::NEW,
            'price' => $options['price_to_pay'] ?? $plan->price,
            'starts_at' => $options['starts_at'] ?? now(),
            'ends_at' => $options['ends_at'] ?? now()->addMonths($plan->period->value)->endOfDay(),
        ]);
    }

    public function createSubscriptionWithPayment(User $user, Plan $plan, array $options = []): Subscription
    {
        $subscription = $this->createSubscription($user, $plan, $options);
        if ($plan->type === PlanType::TRIAL) {
            $paymentProvider = new InternalPaymentProvider();
            $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
            $this->isPaymentFinished = true;
            $subscription->paymentSuccessful();
            $subscription->tenant->activateSubscription($subscription);
            tenant(true);
            Stats::increment('subscription.trial');
            return $subscription;
        }
        Stats::increment('subscription.created');
        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        return $subscription;
    }


    public function upgradeSubscriptionWithPayment(
        User $user,
        Plan $plan,
        Subscription $oldSubscription,
        array $options = []
    ): Subscription {
        if ($plan->type === PlanType::TRIAL) {
            throw new \InvalidArgumentException('Cannot upgrade to trial subscription');
        }
        $subscription = $this->createSubscription($user, $plan, $options);
        Stats::increment('subscription.upgraded');
        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        $subscription->upgraded_from = $oldSubscription->id;
        $subscription->save();
        return $subscription;
    }


    public function renewSubscriptionWithPayment(
        User $user,
        Plan $plan,
        Subscription $oldSubscription,
        array $options = []
    ): Subscription {
        if ($plan->type === PlanType::TRIAL) {
            throw new \InvalidArgumentException('Cannot renew trial subscription');
        }

        $options['starts_at'] = self::calculateRenewalStartDate($plan, $oldSubscription);
        $options['ends_at'] = Carbon::parse($oldSubscription->ends_at)
            ->addDay()
            ->addMonths($plan->period->value)
            ->endOfDay();
        $subscription = $this->createSubscription($user, $plan, $options);
        Stats::increment('subscription.renewed');
        $paymentProvider = new PayUPaymentProvider();
        $this->continuePaymentUrl = $paymentProvider->charge($user, $subscription, ['currency' => 'PLN']);
        return $subscription;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->cancelSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        SubscriptionCanceled::dispatch($subscription);
        return true;
    }

    public function expireSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::EXPIRED ||
            $subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->expireSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        Mail::to($subscription->user->email)
            ->queue(new SubscriptionEnded($subscription));

        Notification::make()
            ->title('Twoja subskrypcja wygasła!')
            ->body('Wybierz nową subskrypcję aby kontynuować korzystanie z naszych usług.')
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label('Przejdz do panelu subskrypcji')
                    ->button()
                    ->url(SubscriptionManagement::getUrl(), shouldOpenInNewTab: false),
            ])
            ->warning()
            ->sendToDatabase($subscription->user);
        return true;
    }

    public function swapSubscription(Subscription $subscription): bool
    {
        if ($subscription->status === SubscriptionStatus::EXPIRED ||
            $subscription->status === SubscriptionStatus::CANCELED) {
            return true;
        }

        $subscription->tenant->expireSubscription();

        $paymentProvider = new PayUPaymentProvider();
        $paymentProvider->cancelSubscription($subscription);

        $pendingSubscription = $subscription->tenant->getPendingSubscription();
        if ($pendingSubscription) {
            $pendingSubscription->tenant->activateSubscription($pendingSubscription);
            $pendingSubscription->status = SubscriptionStatus::ACTIVE;
            $pendingSubscription->save();
            return true;
        }

        Mail::to($subscription->user->email)
            ->queue(new SubscriptionEnded($subscription));

        Stats::increment('subscription.expired');

        Notification::make()
            ->title('Twoja subskrypcja wygasła!')
            ->body('Wybierz nową subskrypcję aby kontynuować korzystanie z naszych usług.')
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label('Przejdz do panelu subskrypcji')
                    ->button()
                    ->url(SubscriptionManagement::getUrl(), shouldOpenInNewTab: false),
            ])
            ->warning()
            ->sendToDatabase($subscription->user);
        return true;
    }

    public static function isSamePlan(Plan $newPlan, Subscription $currentSubscription): bool
    {
        $currentPlan = $currentSubscription->plan;

        $currentFeatures = $currentPlan->features ?? [];
        $newFeatures = $newPlan->features ?? [];
        sort($currentFeatures);
        sort($newFeatures);

        return $currentPlan->period === $newPlan->period &&
            $currentPlan->type === $newPlan->type &&
            $currentFeatures === $newFeatures;
    }

    public static function calculateRenewalStartDate(Plan $selectedPlan, Subscription $currentSubscription): Carbon
    {
        if (self::isSamePlan($selectedPlan, $currentSubscription)) {
            return Carbon::today();
        }
        return Carbon::parse($currentSubscription->ends_at)->addDay();
    }

    public static function isSubscriptionNearExpiry(Subscription $subscription): bool
    {
        return Carbon::parse($subscription->ends_at)->diffInDays(Carbon::today()) <= 7;
    }

    /**
     * Calculate price for upgrading plan
     *
     * @param Plan $newPlan
     * @param Subscription $currentSubscription
     * @return array<'days_left', 'price_to_pay', 'price_to_refund'>
     */
    public static function calculateUpgradePlanPrice(Plan $newPlan, Subscription $currentSubscription): array
    {
        if ($currentSubscription->plan->period !== $newPlan->period) {
            return [
                'days_left' => 0,
                'price_to_pay' => $newPlan->price,
                'price_to_refund' => 0,
            ];
        }

        if ($currentSubscription->plan->period->value === 12) {
            $daysInPeriod = 365;
        } elseif ($currentSubscription->plan->period->value === 6) {
            $daysInPeriod = 182;
        } elseif ($currentSubscription->plan->period->value === 1) {
            $daysInPeriod = 30;
        }

        $daysLeft = Carbon::parse($currentSubscription->ends_at)->diffInDays(Carbon::today());
        $pricePerDay = $currentSubscription->plan->price / $daysInPeriod;
        $priceToRefund = round($pricePerDay * $daysLeft, 2);

        $newPlanPrice = $newPlan->price;
        $newPlanPricePerDay = $newPlanPrice / $daysInPeriod;
        $priceToPay = round($newPlanPricePerDay * $daysLeft);

        return ['days_left' => $daysLeft, 'price_to_pay' => $priceToPay, 'price_to_refund' => $priceToRefund];
    }
}
