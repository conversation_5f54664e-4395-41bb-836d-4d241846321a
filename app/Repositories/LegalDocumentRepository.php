<?php

namespace App\Repositories;

use App\Enums\LegalDocumentStatus;
use App\Enums\LegalDocumentType;
use App\Models\LegalDocument;
use App\Models\Tenant;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class LegalDocumentRepository
{
    public static string $error = '';
    public static string $directory = 'legal-docs';

    /**
     * Get the current published document for a specific type and tenant
     */
    public static function getCurrentPublished(LegalDocumentType $type): ?LegalDocument
    {
        return LegalDocument::where('document_type', $type)
            ->where('status', LegalDocumentStatus::PUBLISHED)
            ->first();
    }

    /**
     * Get the latest published and rendered document for a specific type
     */
    public static function getLatestRendered(LegalDocumentType $type, $fileType = 'html'): ?string
    {
        $path = self::$directory . '/' . $type->slug() . '.' . ($fileType === 'html' ? 'html' : 'pdf');
        return Storage::disk('public')->exists($path) ?
            Storage::disk('public')->get($path) : null;
    }

    /**
     * Get all drafts for a specific type and tenant
     */
    public static function getDrafts(LegalDocumentType $type): Collection
    {
        return LegalDocument::where('document_type', $type)
            ->where('status', LegalDocumentStatus::DRAFT)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get version history for a specific type and tenant
     */
    public static function getVersionHistory(LegalDocumentType $type): Collection
    {
        return LegalDocument::where('document_type', $type)
            ->whereIn('status', [LegalDocumentStatus::PUBLISHED, LegalDocumentStatus::ARCHIVED])
            ->orderBy('version_number', 'desc')
            ->get();
    }

    /**
     * Create a new draft document
     */
    public static function createDraft(array $data): LegalDocument
    {
        $document = new LegalDocument();
        $document->title = $data['title'];
        $document->content = $data['content'];
        $document->document_type = $data['document_type'];
        $document->status = LegalDocumentStatus::DRAFT;
        $document->version_number = self::getNextVersionNumber($data['document_type']);
        $document->save();

        return $document;
    }

    /**
     * Clone a published document to create a new draft
     */
    public static function cloneToNewDraft(LegalDocument $sourceDocument): LegalDocument
    {
        $newDocument = new LegalDocument();
        $newDocument->title = $sourceDocument->title;
        $newDocument->content = $sourceDocument->content;
        $newDocument->document_type = $sourceDocument->document_type;
        $newDocument->status = LegalDocumentStatus::DRAFT;
        $newDocument->version_number = self::getNextVersionNumber(
            $sourceDocument->document_type
        );
        $newDocument->save();

        return $newDocument;
    }

    /**
     * Publish a draft document
     */
    public static function publishDraft(LegalDocument $document): bool
    {
        if (!$document->canBePublished()) {
            self::$error = 'Document cannot be published in its current state';
            return false;
        }

        try {
            DB::transaction(function () use ($document) {
                // Archive any existing published document of the same type
                LegalDocument::where('document_type', $document->document_type)
                    ->where('status', LegalDocumentStatus::PUBLISHED)
                    ->update(['status' => LegalDocumentStatus::ARCHIVED]);

                $document->status = LegalDocumentStatus::PUBLISHED;
                $document->publication_date = now();

                self::generateFiles($document);

                $document->save();
            });

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Generate HTML and PDF files for a document
     */
    public static function generateFiles(LegalDocument $document): void
    {
        $baseName = $document->getFileBaseName();
        Storage::disk('public')->makeDirectory(self::$directory);

        // Generate HTML
        $htmlContent = self::renderHtml($document);
        $htmlPath = self::getFilePath($document, 'html');
        Storage::disk('public')->put($htmlPath, $htmlContent);
        $document->html_file_path = $htmlPath;

        // Generate PDF
        $pdf = self::generatePdf($document);
        $pdfPath = self::getFilePath($document, 'pdf');
        Storage::disk('public')->put($pdfPath, $pdf->output());
        $document->pdf_file_path = $pdfPath;

        self::makeSymLinks($document);
    }

    protected static function makeSymLinks(LegalDocument $document): void
    {
        $baseName = $document->getFileBaseName();
        $dirPath = Storage::disk('public')->path(self::$directory);
        $targetFname = $document->document_type->slug();

        // remove symlinks
        Storage::disk('public')->delete(self::$directory . '/' . $targetFname . '.pdf');
        Storage::disk('public')->delete(self::$directory . '/' . $targetFname . '.html');

        //create symlinks
        File::link($baseName.".html", $dirPath . '/' . $targetFname . '.html');
        File::link($baseName.".pdf", $dirPath . '/' . $targetFname . '.pdf');
    }


    protected static function getFilePath(LegalDocument $document, string $extension): string
    {
        return self::$directory . "/{$document->getFileBaseName()}.{$extension}";
    }

    /**
     * Render HTML content for a document
     */
    public static function renderHtml(LegalDocument $document): string
    {
        $templatePath = "legal.{$document->document_type->value}";

        if (!View::exists($templatePath)) {
            $templatePath = 'legal.default';
        }

        return view($templatePath, [
            'document' => $document,
            'tenant' => $document->tenant,
        ])->render();
    }

    /**
     * Generate PDF for a document
     */
    public static function generatePdf(LegalDocument $document): \Barryvdh\DomPDF\PDF
    {
        $html = self::renderHtml($document);
        return Pdf::loadHtml($html);
    }

    /**
     * Get the next version number for a document type
     */
    public static function getNextVersionNumber(LegalDocumentType $type): int
    {
        $maxVersion = LegalDocument::where('document_type', $type)
            ->max('version_number');

        return ($maxVersion ?? 0) + 1;
    }

    /**
     * Reset error messages
     */
    public static function resetErrors(): void
    {
        self::$error = '';
    }

    public static function getDocTypeBySlug(string $slug): ?LegalDocumentType
    {
        return  LegalDocumentType::getIndexedBySlug()[$slug] ?? null;
    }
}
