<?php

namespace App\Jobs;

use App\Mail\TradeDocumentEmail;
use App\Models\TradeDoc;
use App\Repositories\TenantRepository;
use App\Repositories\TradeDocsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SendTradeDocEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public TradeDoc $tradeDoc,
        public string $recipientEmail,
        public ?string $currentUserEmail = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $attachments = [];

        try {
            $variant = TenantRepository::getTenantTradeDocVariant($this->tradeDoc->tenant);

            if ($variant) {
                $pdf = TradeDocsRepository::getPDFData($this->tradeDoc, $variant);
                $filename = Str::of($this->tradeDoc->full_doc_number)
                        ->replace('/', '_')
                        ->title()
                        ->snake() . '.pdf';

                $savePath = 'tenants/' . $this->tradeDoc->tenant->hash . '/trade_docs/';
                Storage::disk('local')->put($savePath . $filename, $pdf->output());
                $attachments[] = Attachment::fromStorage(
                    $savePath . $filename,
                )->withMime('application/pdf')
                ->as($filename);
            }
        } catch (\Exception $e) {
            Log::error('Failed to attach PDF to trade document email', [
                'trade_doc_id' => $this->tradeDoc->uuid,
                'error' => $e->getMessage()
            ]);
        }

        if (count($attachments) > 0) {
            Mail::to($this->recipientEmail)
                ->send(new TradeDocumentEmail(
                    $this->tradeDoc,
                    $this->recipientEmail,
                    $attachments
                ));

            if ($this->currentUserEmail) {
                Mail::to($this->currentUserEmail)
                    ->send(
                        new TradeDocumentEmail(
                            $this->tradeDoc,
                            $this->currentUserEmail,
                            $attachments
                        )
                    );
            }
        }
    }
}
