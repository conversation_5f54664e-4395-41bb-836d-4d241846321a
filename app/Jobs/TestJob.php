<?php

namespace App\Jobs;

use App\Models\Tenant;
use App\Models\TradeDocItem;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public string $data)
    {
        //
    }
    /**
     * Execute the job.
     */
    public function handle()
    {
        $res = TradeDocItem::query()->whereBetween('net_value', range(100, 50000))->count();
        Log::info("Found $res items for job: " . $this->data);
        sleep(0.1);
        return "Job completed successfully for data: " . $this->data;
    }
}
