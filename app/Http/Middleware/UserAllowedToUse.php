<?php

namespace App\Http\Middleware;

use App\Scopes\NoSuperAdminInList;
use App\Scopes\OnlyTenantUsers;
use Closure;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class UserAllowedToUse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = tenant(true);
        if ($tenant !== null && $tenant->is_active) {
            if (!defined('INSTALLATION')) {
                define('INSTALLATION', $tenant->id);
            }
            auth()->user()::addGlobalScope(new NoSuperAdminInList());
            auth()->user()::addGlobalScope(new OnlyTenantUsers());
            return $next($request);
        }
        Filament::auth()->logout();
        Session::invalidate();
        Notification::make()
            ->title('Błąd 426')
            ->persistent()
            ->danger()
            ->send();
        return redirect(Filament::getLoginUrl());
    }
}
