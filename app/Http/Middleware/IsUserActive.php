<?php

namespace App\Http\Middleware;

use Closure;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class IsUserActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (false === (auth()->user()?->active ?? false)) {
            Filament::auth()->logout();
            Session::invalidate();
            Notification::make()
                ->title('Błąd 425')
                ->persistent()
                ->danger()
                ->send();
            return redirect(Filament::getLoginUrl());
        }

        return $next($request);
    }
}
