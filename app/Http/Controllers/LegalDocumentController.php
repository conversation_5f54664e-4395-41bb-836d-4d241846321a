<?php

namespace App\Http\Controllers;

use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class LegalDocumentController extends Controller
{
    /**
     * Preview a legal document in HTML format
     */
    public function preview(LegalDocument $legalDocument): Response
    {
        if (!auth()->user()->isGod()) {
            abort(403, 'Access denied');
        }

        $html = LegalDocumentRepository::renderHtml($legalDocument);

        return response($html, 200, [
            'Content-Type' => 'text/html',
        ]);
    }

    /**
     * Download a legal document as PDF
     */
    public function downloadPdf(LegalDocument $legalDocument): StreamedResponse
    {
        if (!auth()->user()->isGod()) {
            abort(403, 'Access denied');
        }

        $pdf = LegalDocumentRepository::generatePdf($legalDocument);
        $filename = $legalDocument->getFileBaseName() . '.pdf';

        return response()->streamDownload(
            function () use ($pdf) {
                echo $pdf->stream();
            },
            $filename,
            [
                'Content-Type' => 'application/pdf',
            ]
        );
    }

    /**
     * Get the current published document for public access
     */
    public function getPublished(string $type): Response
    {

        $documentType = LegalDocumentRepository::getDocTypeBySlug($type);

        abort_if($documentType === null, 404, 'Document not found');

        $document = LegalDocumentRepository::getLatestRendered($documentType, 'html');

        if ($document) {
            return response($document, 200, [
                'Content-Type' => 'text/html',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'X-Src' => 'File',
            ]);
        }

        $document = LegalDocumentRepository::getCurrentPublished($documentType);

        if (!$document) {
            abort(404, 'Document not found');
        }

        $html = LegalDocumentRepository::renderHtml($document);

        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
        ]);
    }

    /**
     * Get the current published document as PDF for public access
     */
    public function getPublishedPdf(string $type): StreamedResponse
    {
        $documentType = LegalDocumentRepository::getDocTypeBySlug($type);

        abort_if($documentType === null, 404, 'Document not found');

        $document = LegalDocumentRepository::getLatestRendered($documentType, 'pdf');

        if ($document) {
            return response()->streamDownload(
                function () use ($document) {
                    echo $document;
                },
                $type . '.pdf',
                [
                    'Content-Type' => 'application/pdf',
                ]
            );
        }

        $document = LegalDocumentRepository::getCurrentPublished($documentType);

        if (!$document) {
            abort(404, 'Document not found');
        }

        $pdf = LegalDocumentRepository::generatePdf($document);
        $filename = $type . '.pdf';

        return response()->streamDownload(
            function () use ($pdf) {
                echo $pdf->stream();
            },
            $filename,
            [
                'Content-Type' => 'application/pdf',
            ]
        );
    }
}
