<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;

class StripePaymentProvider implements PaymentProviderContract
{

    /**
     * @inheritDoc
     */
    public function createSubscription(User $user, Plan $plan, array $options = []): mixed
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function cancelSubscription(Subscription $subscription): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function getSubscriptionStatus(Subscription $subscription): string
    {
        return '';
    }

    /**
     * @inheritDoc
     */
    public function charge(User $user, Subscription $subscription, array $options = []): mixed
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function handleWebhook(Request $request): void
    {
        // TODO: Implement handleWebhook() method.
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'stripe';
    }

    public function cancelPayment(Payment $payment, array $options = []): bool
    {
        return true;
    }

    public function getContinuePaymentLink(Payment $payment): string
    {
        return '';
    }
}
