<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Enums\PaymentStatus;
use App\Enums\SubscriptionStatus;
use App\Events\PaymentCompleted;
use App\Helpers\Identifiers;
use App\Jobs\PaymentCompletedJob;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class InternalPaymentProvider implements PaymentProviderContract
{

    public null|Subscription $subscription = null;
    public null|Plan $plan = null;
    public null|Payment $payment = null;

    /**
     * @inheritDoc
     */
    public function createSubscription(User $user, Plan $plan, array $options = []): mixed
    {
        /**
         * @var Subscription $sub
         */
        $sub = Subscription::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'plan_id' => $plan->id,
            'price' => $plan->price,
            'status' => SubscriptionStatus::NEW,
            'starts_at' => now(),
            'ends_at' => now()->addMonths($plan->period->value)->endOfDay(),
        ]);
        $sub->setRelation('plan', $plan);
        $this->subscription = $sub;
        $this->plan = $plan;
        $this->charge($user, $sub, ['subscription_id' => $sub->id]);
        return $sub;
    }

    /**
     * @inheritDoc
     */
    public function cancelSubscription(Subscription $subscription): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function getSubscriptionStatus(Subscription $subscription): string
    {
        return $subscription->status->label();
    }

    /**
     * @inheritDoc
     */
    public function charge(User $user, Subscription $subscription, array $options = []): mixed
    {
        $paymentHash = Identifiers::getRandomHash();
        $this->payment = Payment::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'subscription_id' => $subscription->id,
            'provider' => static::class,
            'provider_payment_id' => $paymentHash,
            'amount' => $subscription->plan->getPriceInCents(),
            'currency' => $options['currency'] ?? 'PLN',
            'status' => PaymentStatus::COMPLETED,
            'paid_at' => Carbon::now(),
            'payment_method' => 'internal',
            'payment_transaction_id' => $paymentHash,
        ]);

        (new PaymentCompletedJob($this->payment, []))->handle();

        return true;
    }

    /**
     * @inheritDoc
     */
    public function handleWebhook(Request $request): void
    {
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'internal';
    }

    public function getContinuePaymentLink(Payment $payment): string
    {
        return '';
    }

    public function cancelPayment(Payment $payment, array $options = []): bool
    {
        return true;
    }
}
