<?php
namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;

class Przelewy24PaymentProvider implements PaymentProviderContract
{
    public function createSubscription(User $user, Plan $plan, array $options = []): mixed
    {
        return true;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        return true;
    }

    public function getSubscriptionStatus(Subscription $subscription): string
    {
        return $subscription->status->label();
    }

    public function charge(User $user, Subscription $subscription, array $options = []): mixed
    {
        return true;
    }

    public function handleWebhook(Request $request): void
    {
        // Obsługa powiadomień od P24 (np. transakcja zakończona sukcesem)
    }

    public function getName(): string
    {
        return 'przelewy24';
    }

    public function cancelPayment(Payment $payment, array $options = []): bool
    {
        return true;
    }

    public function getContinuePaymentLink(Payment $payment): string
    {
        return '';
    }
}
