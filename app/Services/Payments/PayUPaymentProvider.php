<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Enums\PaymentStatus;
use App\Jobs\PaymentCanceledJob;
use App\Jobs\PaymentCompletedJob;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use App\Repositories\SubscriptionsRepository;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class PayUPaymentProvider implements PaymentProviderContract
{

    public null|Subscription $subscription = null;
    public null|Plan $plan = null;
    public null|Payment $payment = null;
    public null|string $error = null;

    public function __construct()
    {
        $this->setupPayU();
    }

    public function createSubscription(User $user, Plan $plan, array $options = []): mixed
    {

        $subRepo = new SubscriptionsRepository();

        /**
         * @var Subscription $sub
         */
        $sub = $subRepo->createSubscription($user, $plan, $options);
        $sub->setRelation('plan', $plan);
        $this->subscription = $sub;
        $this->plan = $plan;
        $this->charge($user, $sub, ['subscription_id' => $sub->id]);
        return $sub;
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        return true;
    }

    public function getSubscriptionStatus(Subscription $subscription): string
    {
        // Zwraca lokalny status lub wykonuje zapytanie do API (jeśli obsługujesz recurring przez PayU)
        return $subscription->status->name;
    }

    public function charge(User $user, Subscription $subscription, array $options = []): ?string
    {
        $this->payment = Payment::create([
            'user_id' => $user->id,
            'tenant_id' => $user->installation(),
            'subscription_id' => $subscription->id,
            'provider' => static::class,
            'amount' => $subscription->getPriceInCents(),
            'currency' => $options['currency'] ?? 'PLN',
            'status' => PaymentStatus::NEW
        ]);

        $order['continueUrl'] = route('payment-thank-you', ['paymentId' => $this->payment->hash]);
        $order['notifyUrl'] = route('payments-webhook', ['provider' => 'payu']);
        $order['customerIp'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $order['merchantPosId'] = \OpenPayU_Configuration::getMerchantPosId();
        $order['description'] = 'Subskrypcja ' . $subscription->getOrderId();
        $order['currencyCode'] = $options['currency'] ?? 'PLN';
        $order['totalAmount'] = (int)$subscription->getPriceInCents();
        $order['extOrderId'] = $subscription->getOrderId(); //must be unique!

        $order['products'][0]['name'] = $subscription->plan->name;
        $order['products'][0]['unitPrice'] = (int)$subscription->getPriceInCents();
        $order['products'][0]['quantity'] = 1;

        $order['buyer']['email'] = $user->email;
        $order['buyer']['firstName'] = $user->profile->name ?? '';
        $order['buyer']['lastName'] = $user->profile->surname ?? '';

        try {
            $response = \OpenPayU_Order::create($order);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            $this->payment->update([
                'status' => PaymentStatus::ERROR,
                'meta' => ['error' => $e->getMessage()]
            ]);
            return null;
        }

        if (null === $response) {
            Log::error('Payment response is empty');
            $this->payment->update([
                'status' => PaymentStatus::ERROR,
                'meta' => ['error' => 'Payment response is empty']
            ]);
            return null;
        }


        $this->payment->update([
            'provider_payment_id' => $response->getResponse()->orderId,
            'status' => $response->getResponse()->status->statusCode === 'SUCCESS' ?
                PaymentStatus::PENDING :
                PaymentStatus::ERROR,
            'meta' => $response->getResponse(),
        ]);

        return $response->getResponse()->redirectUri;
    }

    public function handleWebhook(Request $request): void
    {

//        if (!$this->checksum($request)) {
//            Log::error('Payment checksum failed');
//            return;
//        }

        $data = $request->all();

        if (blank($data['order']['orderId'] ?? [])) {
            Log::info('Payment payload incomplete', $data);
            return;
        }

        $this->payment = Payment::where('provider_payment_id', $data['order']['orderId'])
            ->where('provider', static::class)
            ->first();

        if (blank($this->payment)) {
            Log::error('Payment not found');
            return;
        }

        if ($this->payment->status === PaymentStatus::COMPLETED) {
            Log::info('Payment already completed');
            return;
        }

        $data['checksum'] = $this->getChecksum($request);

        $this->payment->webhooks()->create([
            'payload' => $data
        ]);

        $this->subscription = $this->payment->subscription;
        if ($data['order']['status'] === PaymentStatus::CANCELED->name) {
            $this->payment->update([
                'status' => $data['order']['status'],
            ]);
            PaymentCanceledJob::dispatch($this->payment);
            return;
        }


        if ($data['order']['status'] === PaymentStatus::COMPLETED->name) {
            PaymentCompletedJob::dispatch($this->payment, $data);
        }
    }

    public function getName(): string
    {
        return 'payu';
    }

    public function cancelPayment(Payment $payment, array $options = []): bool
    {
        try {
            $response = \OpenPayU_Order::cancel($payment->provider_payment_id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            $this->error = $e->getMessage();
            return false;
        }

        if (null === $response) {
            Log::error('Payment response is empty');
            $this->error = 'Payment response is empty';
            return false;
        }

        if ($response->getStatus() !== 'SUCCESS') {
            $responseArray = json_decode(json_encode($response->getResponse()), true);
            Log::error('Payment cancellation failed: ' .
                ($responseArray['status']['error_desc'] ?? $responseArray['status']['statusDesc'] ?? 'Unknown error'));
            $this->error = 'Payment cancellation failed: ' .
                ($responseArray['status']['error_desc'] ?? $responseArray['status']['statusDesc'] ?? 'Unknown error');
            return false;
        }

        if ($response->getResponse()->status->statusCode === 'SUCCESS') {
            $payment->update(['status' => PaymentStatus::CANCELED->name]);
            return true;
        }

        Log::error('Payment cancellation problem: ' .
            $response->getStatus() . ' ' .
            $response->getError());
        $this->error = 'Payment cancellation failed: ' . $response->getStatus() . ' ' . $response->getError();
        return false;
    }

    protected function getPayUConfig(): array
    {
        return [
            'pos_id' => config('services.payu.pos_id'),
            'second_key' => config('services.payu.second_key'),
            'client_id' => config('services.payu.client_id'),
            'client_secret' => config('services.payu.client_secret'),
            'environment' => config('services.payu.environment'),
            'webhook_url' => config('services.payu.webhook_code'),
        ];
    }

    protected function setupPayU(): void
    {
        $config = $this->getPayUConfig();

        \OpenPayU_Configuration::setEnvironment($config['environment']);
        \OpenPayU_Configuration::setMerchantPosId($config['pos_id']);
        \OpenPayU_Configuration::setSignatureKey($config['second_key']);
        \OpenPayU_Configuration::setOauthClientId($config['client_id']);
        \OpenPayU_Configuration::setOauthClientSecret($config['client_secret']);
        (new Filesystem())->makeDirectory(
            path: storage_path('framework/cache/payu'),
            mode: 0775,
            recursive: true,
            force: true
        );
        \OpenPayU_Configuration::setOauthTokenCache(new \OauthCacheFile(storage_path('framework/cache/payu')));
    }

    protected function checksum(Request $request): bool
    {
        $signatureHeader = $request->header('x-openpayu-signature', '');
        $headers = explode(";", $signatureHeader);
        if (blank($headers['signature'])) {
            return false;
        }
        return md5($request->getContent() . \OpenPayU_Configuration::getSignatureKey()) === $headers['signature'];
    }

    protected function getChecksum(Request $request): array
    {
        $signatureHeader = $request->header('x-openpayu-signature', '');
        $headers = [];
        array_map(
            static function ($header) use (&$headers) {
                $row = explode("=", $header);
                $headers[$row[0]] = $row[1];
            },
            explode(";", $signatureHeader) ?? []
        );
        return [
            'headers' => $signatureHeader ?? null,
            'sender' => $headers['sender'] ?? null,
            'signature' => $headers['signature'] ?? null,
            'content' => $request->getContent(),
            'key' => \OpenPayU_Configuration::getSignatureKey(),
            'checksum' => md5($request->getContent() . \OpenPayU_Configuration::getSignatureKey())
        ];
    }

    public function getContinuePaymentLink(Payment $payment): string
    {
        if ($payment->provider !== static::class) {
            return '';
        }

        $meta = $payment->meta;
        return $meta['redirectUri'] ?? '';
    }
}
