<?php

namespace App\Services;

use App\Models\Tenant;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TenantArchiverService
{

    protected bool $encrypt = false;

    protected ?Tenant $tenant = null;

    protected string $error = '';
    private bool $compress = false;

    private bool $removeSourceFiles = false;

    public function __construct()
    {
        $this->encrypt = config('app.env') === 'production';
    }

    /**
     * Encrypt the archive files
     * @param bool $encrypt
     * @return $this
     */
    public function encrypted(bool $encrypt = true): self
    {
        $this->encrypt = $encrypt;
        return $this;
    }

    /**
     * Compress the archive files
     * @param bool $compress
     * @return $this
     */
    public function compressed(bool $compress = true, bool $removeSourceFiles = false): self
    {
        $this->compress = $compress;
        $this->removeSourceFiles = $removeSourceFiles;
        return $this;
    }

    public function tenant(Tenant $tenant): self
    {
        $this->tenant = $tenant;
        return $this;
    }

    public function getTenant(): ?Tenant
    {
        return $this->tenant;
    }

    /**
     * Archive tenant data before deletion
     *
     * Creates a complete backup of tenant data as separate JSON files:
     * - Tenant model data (including DTOTenantMetadata records)
     * - All User models belonging to the tenant (excluding ProfileData for privacy)
     * - All subscription records with Plan relationships
     * - All payment records and their associated webhook data
     *
     * @param Tenant $tenant The tenant to archive
     * @return bool Success/failure status
     */
    public function archiveTenantData(?Tenant $tenant = null): bool
    {
        $tenant ??= $this->tenant;

        if (null === $tenant) {
            $this->error = 'No tenant provided';
            return false;
        }

        try {
            Log::info('Starting tenant data archival for tenant: ' . $tenant->name, ['tenant_id' => $tenant->id]);

            $archiveDir = $this->getArchiveDir($tenant);

            if (!Storage::disk('local')->exists($archiveDir)) {
                (new Filesystem())->makeDirectory(Storage::disk('local')->path($archiveDir), 0755, true);
            }

            $this->archiveTenantModelData($tenant, $archiveDir);
            $this->archiveUsersData($tenant, $archiveDir);
            $this->archiveSubscriptionsData($tenant, $archiveDir);
            $this->archivePaymentsData($tenant, $archiveDir);

            Log::info(
                'Successfully completed tenant data archival for tenant: ' .
                $tenant->name,
                ['tenant_id' => $tenant->id]
            );

            if ($this->compress) {
                $this->zipExport($archiveDir);
                if ($this->removeSourceFiles) {
                    Storage::disk('local')->deleteDirectory($archiveDir);
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to archive tenant data for tenant: ' . $tenant->name, [
                'tenant_id' => $tenant->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            $this->error = $e->getMessage();
            return false;
        }
    }

    protected function archiveTenantModelData(Tenant $tenant, string $archiveDir): void
    {
        Log::debug('Archiving tenant model data for tenant: ' . $tenant->name);

        $tenant->load(['meta']);

        $tenantData = $tenant->toArray();

        // Add enum values as readable strings
        $tenantData['vat_type_name'] = $tenant->vat_type?->name;
        $tenantData['tax_residency_country_name'] = $tenant->tax_residency_country?->name;
        $tenantData['tax_type_name'] = $tenant->tax_type?->name;
        $tenantData['accounting_type_name'] = $tenant->accounting_type?->name;
        $tenantData['business_type_name'] = $tenant->business_type?->name;

        // Add archive metadata
        $archiveData = [
            'archived_at' => Carbon::now()->toISOString(),
            'archive_version' => '1.0',
            'tenant_data' => $tenantData
        ];

        $this->saveJsonFile($archiveDir, 'tenant.json', $archiveData);

        Log::debug('Completed archiving tenant model data for tenant: ' . $tenant->name);
    }

    /**
     * Archive users data (excluding ProfileData for privacy)
     */
    protected function archiveUsersData(Tenant $tenant, string $archiveDir): void
    {
        Log::debug('Archiving users data for tenant: ' . $tenant->name);

        $usersData = [];

        $tenant->user()->withTrashed()->chunk(100, function ($users) use (&$usersData) {
            foreach ($users as $user) {
                $userData = $user->makeHidden(['password', 'remember_token'])->toArray();
                $userData['roles'] = $user->roles->pluck('name')->toArray();
                $userData['profile_excluded'] = 'ProfileData excluded for privacy reasons';
                $usersData[] = $userData;
            }
        });

        $archiveData = [
            'archived_at' => Carbon::now()->toISOString(),
            'archive_version' => '1.0',
            'total_users' => count($usersData),
            'note' => 'ProfileData relationships excluded for privacy protection',
            'users_data' => $usersData
        ];

        $this->saveJsonFile($archiveDir, 'users.json', $archiveData);

        Log::debug('Completed archiving users data for tenant: ' .
            $tenant->name .
            ' (Total: ' . count($usersData) . ' users)');
    }

    /**
     * Archive subscriptions data with Plan relationships
     */
    protected function archiveSubscriptionsData(Tenant $tenant, string $archiveDir): void
    {
        Log::debug('Archiving subscriptions data for tenant: ' . $tenant->name);

        $subscriptionsData = [];

        $tenant->subscriptions()->with([
            'plan' => function ($query) {
                $query->withTrashed();
            },
            'user' => function ($query) {
                $query->withTrashed();
            }
        ])
            ->chunk(50, function ($subscriptions) use (&$subscriptionsData) {
                foreach ($subscriptions as $subscription) {
                    $subscriptionData = $subscription->makeHidden(['plan', 'user'])->toArray();

                    if ($subscription->plan) {
                        $subscriptionData['plan_details'] = [
                            'id' => $subscription->plan->id,
                            'name' => $subscription->plan->name,
                            'description' => $subscription->plan->description,
                            'price' => $subscription->plan->price,
                            'period' => $subscription->plan->period?->value ?? null,
                            'period_name' => $subscription->plan->period?->name ?? null,
                            'type' => $subscription->plan->type?->name ?? null, // PlanType has no value, only name
                            'type_name' => $subscription->plan->type?->name ?? null,
                            'features' => $subscription->plan->features,
                            'is_active' => $subscription->plan->is_active,
                            'weight' => $subscription->plan->weight,
                            'plan_code' => $subscription->plan->getPlanCode(),
                        ];
                    }

                    if ($subscription->user) {
                        $subscriptionData['user_details'] = [
                            'id' => $subscription->user->id,
                            'email' => $subscription->user->email,
                            'active' => $subscription->user->active,
                        ];
                    }

                    $subscriptionData['status_name'] = $subscription->status?->name;
                    $subscriptionsData[] = $subscriptionData;
                }
            });

        $archiveData = [
            'archived_at' => Carbon::now()->toISOString(),
            'archive_version' => '1.0',
            'total_subscriptions' => count($subscriptionsData),
            'subscriptions_data' => $subscriptionsData
        ];

        $this->saveJsonFile($archiveDir, 'subscriptions.json', $archiveData);

        Log::debug('Completed archiving subscriptions data for tenant: ' .
            $tenant->name .
            ' (Total: ' . count($subscriptionsData) . ' subscriptions)');
    }

    protected function archivePaymentsData(Tenant $tenant, string $archiveDir): void
    {
        Log::debug('Archiving payments data for tenant: ' . $tenant->name);

        $paymentsData = [];
        $tenant->payments()->with(['webhooks', 'subscription'])
            ->chunk(50, function ($payments) use (&$paymentsData) {
                foreach ($payments as $payment) {
                    $paymentData = $payment->makeHidden(['webhooks', 'subscription'])->toArray();
                    $paymentData['meta']['redirectUri'] = 'redacted';

                    $paymentData['webhooks_data'] = [];
                    foreach ($payment->webhooks as $webhook) {
                        $payload = $webhook->payload ?? [];
                        if ($payload['checksum'] ?? false) {
                            $payload['checksum']['headers'] = 'redacted';
                            $payload['checksum']['key'] = 'redacted';
                            $payload['checksum']['content'] = 'redacted';
                        }
                        $paymentData['webhooks_data'][] = [
                            'id' => $webhook->id,
                            'payment_id' => $webhook->payment_id,
                            'payload' => $payload,
                            'created_at' => $webhook->created_at,
                            'updated_at' => $webhook->updated_at,
                        ];
                    }

                    if ($payment->subscription) {
                        $paymentData['subscription_details'] = [
                            'id' => $payment->subscription->id,
                            'status' => $payment->subscription->status?->name ?? null,
                            'status_name' => $payment->subscription->status?->name ?? null,
                            'starts_at' => $payment->subscription->starts_at,
                            'ends_at' => $payment->subscription->ends_at,
                            'trial_ends_at' => $payment->subscription->trial_ends_at,
                        ];
                    }

                    $payment->load(['user' => function ($query) {
                        $query->withTrashed();
                    }]);

                    if ($payment->user) {
                        $paymentData['user_details'] = [
                            'id' => $payment->user->id,
                            'email' => $payment->user->email,
                        ];
                    }

                    $paymentData['status_name'] = $payment->status?->name;
                    $paymentsData[] = $paymentData;
                }
            });

        $archiveData = [
            'archived_at' => Carbon::now()->toISOString(),
            'archive_version' => '1.0',
            'total_payments' => count($paymentsData),
            'payments_data' => $paymentsData
        ];

        $this->saveJsonFile($archiveDir, 'payments.json', $archiveData);

        Log::debug('Completed archiving payments data for tenant: ' .
            $tenant->name .
            ' (Total: ' . count($paymentsData) . ' payments)');
    }

    protected function saveJsonFile(string $archiveDir, string $filename, array $data): void
    {
        $jsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        if ($jsonContent === false) {
            throw new \Exception("Failed to encode data to JSON for file: {$filename}");
        }

        $filePath = $archiveDir . '/' . $filename;

        if ($this->encrypt) {
            $jsonContent = Crypt::encryptString($jsonContent);
            $filePath .= '.enc';
        }

        if (!Storage::disk('local')->put($filePath, $jsonContent)) {
            throw new \Exception("Failed to save JSON file: {$filePath}");
        }

        Log::debug("Successfully saved archive file: {$filePath}");
    }

    protected function zipExport($archiveDir): void
    {
        $zip = new \ZipArchive();
        $zip->open(Storage::disk('local')->path($archiveDir . '.zip'), \ZipArchive::CREATE);
        $files = Storage::disk('local')->files($archiveDir);
        foreach ($files as $file) {
            $zip->addFile(Storage::disk('local')->path($file), basename($file));
        }
        $zip->close();
    }

    public function getArchiveDir(Tenant $tenant): string
    {
        return "tenant-export/archive/{$tenant->hash}";
    }
}
