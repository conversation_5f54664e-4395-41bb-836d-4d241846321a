<?php

namespace App\Services;

use App\Models\DailyStatistic;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class StatsService
{
    protected $redis;
    protected $keyPrefix;
    protected $ttlDays;

    public function __construct()
    {
        $connection = config('stats.redis.connection', 'default');
        $this->redis = Redis::connection($connection);
        $this->keyPrefix = config('stats.redis.key_prefix', 'stats');
        $this->ttlDays = config('stats.redis.ttl_days', 7);
    }

    /**
     * Increment a statistic counter for today
     */
    public function increment(string $eventName, int $amount = 1, ?Carbon $date = null): int
    {
        if (!config('stats.enabled', true)) {
            return 0;
        }

        $date = $date ?? Carbon::today();
        $key = $this->buildRedisKey($date, $eventName);

        try {
            $newValue = $this->redis->incrby($key, $amount);

            // Set TTL on first increment
            if ($newValue === $amount) {
                $this->redis->expire($key, $this->ttlDays * 24 * 60 * 60);
            }

            $this->logIfEnabled(
                'info',
                "Incremented {$eventName} by {$amount} for {$date->format('Y-m-d')}, new value: {$newValue}"
            );

            return $newValue;
        } catch (\Exception $e) {
            $this->logIfEnabled('error', "Failed to increment {$eventName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get current count for an event on a specific date
     */
    public function getCount(string $eventName, ?Carbon $date = null): int
    {
        $date = $date ?? Carbon::today();
        $key = $this->buildRedisKey($date, $eventName);

        try {
            $count = $this->redis->get($key);
            return (int) ($count ?? 0);
        } catch (\Exception $e) {
            $this->logIfEnabled('error', "Failed to get count for {$eventName}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get all statistics for a specific date from Redis
     */
    public function getDailyStats(?Carbon $date = null): array
    {
        $date = $date ?? Carbon::today();
        $pattern = $this->buildRedisKey($date, '*');

        try {
            $keys = $this->redis->keys($pattern);
            $stats = [];

            if (!empty($keys)) {
                foreach ($keys as $key) {
                    // Extract the key without prefix for the get operation
                    $keyWithoutPrefix = $this->removeRedisPrefix($key);
                    $value = $this->redis->get($keyWithoutPrefix);
                    $eventName = $this->extractEventNameFromKey($key);
                    $stats[$eventName] = (int) ($value ?? 0);
                }
            }

            return $stats;
        } catch (\Exception $e) {
            $this->logIfEnabled('error', "Failed to get daily stats for {$date->format('Y-m-d')}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Aggregate daily statistics from Redis to database
     */
    public function aggregateDailyStats(?Carbon $date = null): int
    {
        $date = $date ?? Carbon::yesterday();
        $stats = $this->getDailyStats($date);
        $aggregatedCount = 0;

        try {
            foreach ($stats as $eventName => $count) {
                if ($count > 0) {
                    DailyStatistic::updateOrCreate(
                        [
                            'date' => $date->format('Y-m-d'),
                            'event_name' => $eventName,
                        ],
                        [
                            'count' => $count,
                            'metadata' => [
                                'aggregated_at' => now()->toISOString(),
                                'source' => 'redis',
                            ],
                        ]
                    );
                    $aggregatedCount++;
                }
            }

            $this->logIfEnabled('info', "Aggregated {$aggregatedCount} statistics for {$date->format('Y-m-d')}");

            return $aggregatedCount;
        } catch (\Exception $e) {
            $this->logIfEnabled('error', "Failed to aggregate stats for {$date->format('Y-m-d')}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Clear Redis statistics for a specific date
     */
    public function clearDailyRedisStats(?Carbon $date = null): int
    {
        $date = $date ?? Carbon::yesterday();
        $pattern = $this->buildRedisKey($date, '*');

        try {
            $keys = $this->redis->keys($pattern);
            $deletedCount = 0;

            if (!empty($keys)) {
                // Remove prefix from keys before deleting
                $keysWithoutPrefix = array_map([$this, 'removeRedisPrefix'], $keys);
                $deletedCount = $this->redis->del($keysWithoutPrefix);
            }

            $this->logIfEnabled('info', "Cleared {$deletedCount} Redis keys for {$date->format('Y-m-d')}");

            return $deletedCount;
        } catch (\Exception $e) {
            $this->logIfEnabled(
                'error',
                "Failed to clear Redis stats for {$date->format('Y-m-d')}: " . $e->getMessage()
            );
            throw $e;
        }
    }

    /**
     * Get statistics from database for date range
     */
    public function getHistoricalStats(string $eventName, Carbon $startDate, Carbon $endDate): Collection
    {
        return DailyStatistic::getEventStats($eventName, $startDate, $endDate);
    }

    /**
     * Get total count for an event in a date range from database
     */
    public function getTotalCount(string $eventName, Carbon $startDate, Carbon $endDate): int
    {
        return DailyStatistic::getTotalCount($eventName, $startDate, $endDate);
    }

    /**
     * Build Redis key for a date and event
     */
    protected function buildRedisKey(Carbon $date, string $eventName): string
    {
        return "{$this->keyPrefix}:{$date->format('Y-m-d')}:{$eventName}";
    }

    /**
     * Remove Redis prefix from key
     */
    protected function removeRedisPrefix(string $key): string
    {
        $prefix = config('database.redis.options.prefix', '');
        if ($prefix && str_starts_with($key, $prefix)) {
            return substr($key, strlen($prefix));
        }
        return $key;
    }

    /**
     * Extract event name from Redis key
     */
    protected function extractEventNameFromKey(string $key): string
    {
        // Remove the Redis prefix if it exists
        $keyWithoutPrefix = $this->removeRedisPrefix($key);

        $parts = explode(':', $keyWithoutPrefix);
        return end($parts);
    }

    /**
     * Log message if logging is enabled
     */
    protected function logIfEnabled(string $level, string $message): void
    {
        if (config('stats.logging.enabled', true)) {
            $channel = config('stats.logging.channel', 'single');
            $configLevel = config('stats.logging.level', 'info');

            // Only log if the level is appropriate
            $levels = ['debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency'];
            $currentLevelIndex = array_search($configLevel, $levels);
            $messageLevelIndex = array_search($level, $levels);

            if ($messageLevelIndex >= $currentLevelIndex) {
                Log::channel($channel)->$level("[Stats] {$message}");
            }
        }
    }
}
