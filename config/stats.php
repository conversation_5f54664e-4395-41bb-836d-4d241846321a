<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Statistics Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the statistics service.
    |
    */

    'enabled' => env('STATS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Redis Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Redis connection used by the statistics service.
    |
    */
    'redis' => [
        'connection' => env('STATS_REDIS_CONNECTION', 'default'),
        'key_prefix' => env('STATS_REDIS_PREFIX', 'stats'),
        'ttl_days' => env('STATS_REDIS_TTL_DAYS', 7), // Keep Redis keys for 7 days
    ],

    /*
    |--------------------------------------------------------------------------
    | Aggregation Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for daily statistics aggregation.
    |
    */
    'aggregation' => [
        'enabled' => env('STATS_AGGREGATION_ENABLED', true),
        'queue' => env('STATS_AGGREGATION_QUEUE', 'default'),
        'batch_size' => env('STATS_AGGREGATION_BATCH_SIZE', 1000),
        'clear_redis_after_aggregation' => env('STATS_CLEAR_REDIS_AFTER_AGGREGATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for statistics service logging.
    |
    */
    'logging' => [
        'enabled' => env('STATS_LOGGING_ENABLED', false),
        'channel' => env('STATS_LOG_CHANNEL', 'single'),
        'level' => env('STATS_LOG_LEVEL', 'info'),
    ],
];
