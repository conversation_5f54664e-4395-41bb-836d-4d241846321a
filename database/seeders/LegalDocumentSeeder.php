<?php

namespace Database\Seeders;

use App\Enums\LegalDocumentType;
use App\Models\LegalDocument;
use App\Models\Tenant;
use Illuminate\Database\Seeder;

class LegalDocumentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all tenants
        $tenants = ['a' => 1];

        foreach ($tenants as $tenant) {
            // Create sample Terms of Service documents
            LegalDocument::factory()
                ->tos()
                ->version(1)
                ->published()
                ->create([
                    'title' => 'Terms of Service',
                ]);

            // Create a draft version 2 for ToS
            LegalDocument::factory()
                ->tos()
                ->version(2)
                ->create([
                    'title' => 'Terms of Service (Updated)',
                    'content' => "UPDATED TERMS OF SERVICE

1. ACCEPTANCE OF TERMS
By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement. These terms have been updated to reflect new regulations.

2. USE LICENSE
Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only. Commercial use requires explicit written permission.

3. USER RESPONSIBILITIES
Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account.

4. PROHIBITED USES
You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts.

5. DISCLAIMER
The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied.

6. LIMITATIONS
In no event shall our company be liable for any damages arising out of the use or inability to use our services.

7. MODIFICATIONS
We may revise these terms at any time with notice to users.

8. GOVERNING LAW
These terms are governed by applicable local laws.",
                ]);

            // Create sample GDPR documents
            LegalDocument::factory()
                ->gdpr()
                ->version(1)
                ->published()
                ->create([
                    'title' => 'GDPR Privacy Policy',
                ]);

            // Create an archived version of GDPR
            LegalDocument::factory()
                ->gdpr()
                ->version(1)
                ->archived()
                ->create([
                    'title' => 'GDPR Privacy Policy (Legacy)',
                    'content' => "LEGACY GDPR PRIVACY POLICY

This is an older version of our privacy policy that has been archived.

1. INTRODUCTION
This legacy policy was in effect from [date] to [date].

2. DATA COLLECTION
We collected basic user information for service provision.

3. DATA USE
Data was used solely for service delivery and improvement.

4. USER RIGHTS
Users had basic rights to access and delete their data.

5. CONTACT
For questions about this legacy policy, please contact our support team.",
                ]);
        }
    }
}
