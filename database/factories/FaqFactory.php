<?php

namespace Database\Factories;

use App\Enums\FaqStatus;
use App\Models\Faq;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Faq>
 */
class FaqFactory extends Factory
{
    protected $model = Faq::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Ogólne',
            'Konto użytkownika',
            '<PERSON><PERSON><PERSON><PERSON>ści',
            'Funkcjonalności',
            'Wsparcie techniczne',
            'Bezpieczeństwo',
            'Integracje',
            'Faktury',
            'Magazyn',
            'Produkty'
        ];

        $questions = [
            'Jak mogę zmienić hasło do mojego konta?',
            'Jak dodać nowy produkt do systemu?',
            'Jak skonfigurować integrację z systemem księgowym?',
            '<PERSON><PERSON><PERSON> są dostępne metody płatności?',
            '<PERSON><PERSON> wy<PERSON> raport sprzedaży?',
            '<PERSON><PERSON> skontaktować się z pomocą techniczną?',
            '<PERSON><PERSON> moje dane są bezpieczne?',
            'Jak eksportować dane z systemu?',
            'Jak zarządzać stanami magazynowymi?',
            'Jak utworzyć fakturę?',
            'Jak dodać nowego partnera biznesowego?',
            'Jakie są limity mojego planu?',
            'Jak anulować subskrypcję?',
            'Czy mogę zmienić plan w trakcie okresu rozliczeniowego?',
            'Jak skonfigurować powiadomienia email?'
        ];

        $answers = [
            '<p>Aby zmienić hasło, przejdź do ustawień konta i wybierz opcję "Zmień hasło". Wprowadź aktualne hasło oraz nowe hasło dwukrotnie.</p>',
            '<p>Nowy produkt możesz dodać w sekcji "Produkty" klikając przycisk "Dodaj produkt". Wypełnij wszystkie wymagane pola i zapisz zmiany.</p>',
            '<p>Integrację z systemem księgowym możesz skonfigurować w ustawieniach systemu. Skontaktuj się z naszym zespołem wsparcia w celu uzyskania szczegółowych instrukcji.</p>',
            '<p>Akceptujemy płatności kartą kredytową, przelewem bankowym oraz BLIK. Wszystkie płatności są bezpiecznie przetwarzane.</p>',
            '<p>Raporty sprzedaży możesz wygenerować w sekcji "Raporty". Wybierz zakres dat i typ raportu, a następnie kliknij "Generuj".</p>',
            '<p>Z pomocą techniczną możesz skontaktować się przez formularz kontaktowy, email lub telefon. Szczegóły znajdziesz w sekcji "Kontakt".</p>',
            '<p>Tak, wszystkie dane są szyfrowane i przechowywane zgodnie z najwyższymi standardami bezpieczeństwa oraz RODO.</p>',
            '<p>Dane możesz eksportować w formatach CSV, Excel lub PDF. Opcja eksportu dostępna jest w większości sekcji systemu.</p>',
            '<p>Stany magazynowe zarządzasz w sekcji "Magazyn". Możesz dodawać, edytować i monitorować poziomy zapasów w czasie rzeczywistym.</p>',
            '<p>Fakturę tworzysz w sekcji "Faktury" klikając "Nowa faktura". Wybierz klienta, dodaj pozycje i zapisz dokument.</p>'
        ];

        return [
            'question' => fake()->randomElement($questions),
            'answer' => fake()->randomElement($answers),
            'category' => fake()->randomElement($categories),
            'status' => fake()->randomElement(FaqStatus::cases()),
        ];
    }

    /**
     * Indicate that the FAQ is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => FaqStatus::PUBLISHED,
        ]);
    }

    /**
     * Indicate that the FAQ is draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => FaqStatus::DRAFT,
        ]);
    }

    /**
     * Set specific category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }
}
