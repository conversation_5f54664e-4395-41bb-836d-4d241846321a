<?php

namespace Database\Factories;

use App\Enums\LegalDocumentStatus;
use App\Enums\LegalDocumentType;
use App\Models\LegalDocument;
use App\Repositories\LegalDocumentRepository;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LegalDocument>
 */
class LegalDocumentFactory extends Factory
{
    protected $model = LegalDocument::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $documentType = $this->faker->randomElement(LegalDocumentType::cases());

        return [
            'title' => $this->getTitleForType($documentType),
            'content' => $this->getContentForType($documentType),
            'status' => LegalDocumentStatus::DRAFT,
            'version_number' => 1,
            'publication_date' => null,
            'document_type' => $documentType,
            'html_file_path' => null,
            'pdf_file_path' => null,
        ];
    }

    /**
     * Indicate that the document is published.
     */
    public function published(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => LegalDocumentStatus::PUBLISHED,
            'publication_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'html_file_path' => 'legal-docs/' . $this->faker->uuid() . '.html',
            'pdf_file_path' => 'legal-docs/' . $this->faker->uuid() . '.pdf',
        ]);
    }

    /**
     * Indicate that the document is archived.
     */
    public function archived(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => LegalDocumentStatus::ARCHIVED,
            'publication_date' => $this->faker->dateTimeBetween('-2 years', '-1 month'),
            'html_file_path' => 'legal-docs/' . $this->faker->uuid() . '.html',
            'pdf_file_path' => 'legal-docs/' . $this->faker->uuid() . '.pdf',
        ]);
    }

    /**
     * Indicate that the document is a Terms of Service document.
     */
    public function tos(): static
    {
        return $this->state(fn(array $attributes) => [
            'document_type' => LegalDocumentType::TOS,
            'version_number' => LegalDocumentRepository::getNextVersionNumber(LegalDocumentType::TOS),
            'title' => $this->getTitleForType(LegalDocumentType::TOS),
            'content' => $this->getTosContent(),
        ]);
    }

    /**
     * Indicate that the document is a GDPR document.
     */
    public function gdpr(): static
    {
        return $this->state(fn(array $attributes) => [
            'document_type' => LegalDocumentType::GDPR,
            'title' => $this->getTitleForType(LegalDocumentType::GDPR),
            'content' => $this->getGdprContent(),
        ]);
    }

    public function todp(): static
    {
        return $this->state(fn(array $attributes) => [
            'document_type' => LegalDocumentType::TODP,
            'title' => $this->getTitleForType(LegalDocumentType::TODP),
            'content' => $this->getGdprContent(),
        ]);
    }

    /**
     * Set a specific version number.
     */
    public function version(int $version): static
    {
        return $this->state(fn(array $attributes) => [
            'version_number' => $version,
        ]);
    }

    /**
     * Get title for document type.
     */
    private function getTitleForType(LegalDocumentType $type): string
    {
        return match ($type) {
            LegalDocumentType::TOS => 'Terms of Service',
            LegalDocumentType::GDPR => 'GDPR Privacy Policy',
            LegalDocumentType::TODP => 'TODP Data Protection Processing Regulation',
        };
    }

    /**
     * Get content for document type.
     */
    private function getContentForType(LegalDocumentType $type): string
    {
        return match ($type) {
            LegalDocumentType::TOS => $this->getTosContent(),
            LegalDocumentType::GDPR => $this->getGdprContent(),
            LegalDocumentType::TODP => $this->getTodpContent(),
        };
    }

    /**
     * Get sample Terms of Service content.
     */
    private function getTosContent(): string
    {
        return "TERMS OF SERVICE

1. ACCEPTANCE OF TERMS
By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.

2. USE LICENSE
Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.

3. DISCLAIMER
The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.

4. LIMITATIONS
In no event shall our company or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on our website, even if we or our authorized representative has been notified orally or in writing of the possibility of such damage.

5. ACCURACY OF MATERIALS
The materials appearing on our website could include technical, typographical, or photographic errors. We do not warrant that any of the materials on its website are accurate, complete, or current.

6. LINKS
We have not reviewed all of the sites linked to our website and are not responsible for the contents of any such linked site.

7. MODIFICATIONS
We may revise these terms of service for its website at any time without notice.

8. GOVERNING LAW
These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.";
    }

    private function getTodpContent(): string
    {
        return "TODP Data Protection Processing Regulation

1. ACCEPTANCE OF TERMS
By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.

2. USE LICENSE
Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.

3. DISCLAIMER
The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.

4. LIMITATIONS
In no event shall our company or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on our website, even if we or our authorized representative has been notified orally or in writing of the possibility of such damage.

5. ACCURACY OF MATERIALS
The materials appearing on our website could include technical, typographical, or photographic errors. We do not warrant that any of the materials on its website are accurate, complete, or current.

6. LINKS
We have not reviewed all of the sites linked to our website and are not responsible for the contents of any such linked site.

7. MODIFICATIONS
We may revise these terms of service for its website at any time without notice.

8. GOVERNING LAW
These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.";
    }

    /**
     * Get sample GDPR content.
     */
    private function getGdprContent(): string
    {
        return "GDPR PRIVACY POLICY

1. INTRODUCTION
We are committed to protecting your personal data and respecting your privacy rights. This privacy policy explains how we collect, use, and protect your personal information in accordance with the General Data Protection Regulation (GDPR).

2. DATA CONTROLLER
We are the data controller for the personal data we process about you. Our contact details are provided at the end of this policy.

3. PERSONAL DATA WE COLLECT
We may collect and process the following categories of personal data:
- Identity data (name, username, title)
- Contact data (email address, telephone numbers, addresses)
- Technical data (IP address, browser type, device information)
- Usage data (how you use our website and services)

4. HOW WE USE YOUR PERSONAL DATA
We use your personal data for the following purposes:
- To provide and maintain our services
- To communicate with you about our services
- To improve our website and services
- To comply with legal obligations

5. LEGAL BASIS FOR PROCESSING
We process your personal data based on:
- Your consent
- Performance of a contract
- Compliance with legal obligations
- Legitimate interests

6. DATA SHARING
We do not sell, trade, or otherwise transfer your personal data to third parties without your consent, except as described in this policy.

7. DATA RETENTION
We retain your personal data only for as long as necessary to fulfill the purposes for which it was collected.

8. YOUR RIGHTS
Under GDPR, you have the following rights:
- Right to access your personal data
- Right to rectification
- Right to erasure
- Right to restrict processing
- Right to data portability
- Right to object
- Right to withdraw consent

9. DATA SECURITY
We implement appropriate technical and organizational measures to protect your personal data against unauthorized access, alteration, disclosure, or destruction.

10. CONTACT US
If you have any questions about this privacy policy or our data practices, please contact us using the information provided below.";
    }
}
