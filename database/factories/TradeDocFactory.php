<?php

namespace Database\Factories;

use App\Enums\DocumentTypes;
use App\Enums\PaymentTypes;
use App\Enums\TradeDocVatMethod;
use App\Models\Partner;
use App\Models\Tenant;
use App\Models\TradeDoc;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TradeDoc>
 */
class TradeDocFactory extends Factory
{
    protected $model = TradeDoc::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $issuedAt = $this->faker->dateTimeBetween('-1 year', 'now');
        $sellsDate = $this->faker->dateTimeBetween($issuedAt, 'now');
        $paymentCreditDays = $this->faker->numberBetween(7, 30);
        $paymentDueDate = (clone $issuedAt)->modify("+{$paymentCreditDays} days");

        $net = $this->faker->numberBetween(10000, 100000); // in grosze
        $vatRate = 0.23;
        $vatAmount = (int)($net * $vatRate);
        $gross = $net + $vatAmount;

        return [
            'uuid' => Str::uuid(),
            'source_id' => null,
            'installation' => 1,
            'issuer_id' => Tenant::factory(),
            'seller_id' => null,
            'buyer_id' => Partner::factory(),
            'type' => DocumentTypes::FVS,
            'document_series_id' => 1,
            'full_doc_number' => 'FV/' . $this->faker->numberBetween(1, 9999) . '/' . date('Y'),
            'doc_number' => $this->faker->numberBetween(1, 9999),
            'transaction_id' => Str::random(40),
            'payment_type' => PaymentTypes::BANK_TRANSFER,
            'payment_type_label' => null,
            'payment_credit_days' => $paymentCreditDays,
            'payment_due_date' => $paymentDueDate,
            'payment_date' => null,
            'is_paid' => false,
            'currency' => 'PLN',
            'exchange_rate' => 1.0,
            'currency_rate_date' => null,
            'vat_method' => TradeDocVatMethod::BASE_ON_NET,
            'net' => $net,
            'vat_amount' => $vatAmount,
            'gross' => $gross,
            'issued_at' => $issuedAt,
            'sells_date' => $sellsDate,
            'ksef_ref' => null,
            'ksef_status' => 0,
            'ksef_inv_number' => null,
            'notes' => null,
            'status' => 0,
            'is_accepted' => false,
            'has_correction' => false,
            'is_cancelled' => false,
            'cancelled_at' => null,
            'cancelled_by' => null,
            'creator_id' => User::factory(),
            'payment_reminder_enabled' => false,
            'payment_reminder_days' => null,
            'payment_reminder_sent_at' => null,
        ];
    }

    /**
     * Indicate that the trade doc has payment reminders enabled.
     */
    public function withPaymentReminder(int $days = 3): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_reminder_enabled' => true,
            'payment_reminder_days' => $days,
        ]);
    }

    /**
     * Indicate that the trade doc is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_paid' => true,
            'payment_date' => $this->faker->dateTimeBetween($attributes['issued_at'], 'now'),
        ]);
    }

    /**
     * Indicate that the payment reminder was already sent.
     */
    public function reminderSent(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_reminder_sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Set a specific due date.
     */
    public function dueDate(\DateTime|string $date): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_due_date' => $date,
        ]);
    }
}
