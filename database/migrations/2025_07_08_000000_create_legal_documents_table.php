<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_documents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('content');
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->integer('version_number')->default(1);
            $table->timestamp('publication_date')->nullable();
            $table->string('document_type', 15);
            $table->string('html_file_path')->nullable();
            $table->string('pdf_file_path')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['document_type']);
            $table->index(['document_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_documents');
    }
};
