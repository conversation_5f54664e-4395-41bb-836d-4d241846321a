<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_docs', function (Blueprint $table) {
            $table->boolean('payment_reminder_enabled')->default(false)->after('is_paid');
            $table->integer('payment_reminder_days')->nullable()->after('payment_reminder_enabled');
            $table->timestamp('payment_reminder_sent_at')->nullable()->after('payment_reminder_days');
            
            // Add index for efficient querying of payment reminders
            $table->index(['payment_reminder_enabled', 'payment_due_date', 'payment_reminder_sent_at'], 'idx_payment_reminders');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trade_docs', function (Blueprint $table) {
            $table->dropIndex('idx_payment_reminders');
            $table->dropColumn(['payment_reminder_enabled', 'payment_reminder_days', 'payment_reminder_sent_at']);
        });
    }
};
