# Project Development Guidelines

This document provides essential information for developers working on this Laravel-based application.

## Build/Configuration Instructions

### Docker Environment Setup

The project uses Docker for development and deployment. The following steps are required to set up the environment:

1. **Prerequisites**:
   - Docker and Docker Compose installed
   - Git for version control

2. **Initial Setup**:
   ```bash
   # Clone the repository (if not already done)
   git clone <repository-url>
   cd nativet.web
   
   # Create environment file
   cp .env.example .env
   
   # Configure SSL certificate
   # Place your certificate in docker/nginx/tf2ssl/cf_certificate.pem
   
   # Run the setup script
   ./setupapp.sh force
   ```

3. **Environment Configuration**:
   - The `.env` file contains all necessary configuration
   - Key settings to configure:
     - `APP_KEY`: Generated automatically by the setup script
     - `DB_USERNAME`, `DB_PASSWORD`: Database credentials
     - `REDIS_HOST`: Redis connection (default: redis)
     - `WWWUSER` and `WWWGROUP`: Should match your host user/group IDs

4. **Docker Services**:
   The application consists of several Docker services:
   - `app`: PHP-FPM application container
   - `nginx`: Web server
   - `queue`: Laravel Horizon for queue processing
   - `nightwatch`: Laravel Nightwatch agent for monitoring
   - `cron`: For scheduled tasks
   - `redis`: For caching and queue
   - `mariadb`: Database (MariaDB 11)
   - `mailhog_server`: For email testing (development only)

5. **Manual Container Management**:
   ```bash
   # Start all containers
   docker compose up -d
   
   # Stop all containers
   docker compose down
   
   # View logs
   docker compose logs -f [service-name]
   
   # Execute commands in containers
   docker compose exec app php artisan <command>
   ```

## Testing Information

### Testing Framework

The project uses PHPUnit for testing with two main test types:
- **Unit Tests**: Located in `tests/Unit/`
- **Feature Tests**: Located in `tests/Feature/`

### Running Tests

Tests can be run using Laravel's Artisan command through Docker:

```bash
# Run all tests
docker compose exec app php artisan test

# Run specific test file
docker compose exec app php artisan test --filter=TestClassName

# Run tests with coverage report
docker compose exec app php artisan test --coverage
```

### Creating New Tests

1. **Unit Tests**:
   - Create new test files in `tests/Unit/`
   - Extend `Tests\TestCase`
   - Use `DatabaseTransactions` trait for database tests
   - Focus on testing individual components in isolation

   Example:
   ```php
   <?php
   
   namespace Tests\Unit;
   
   use Tests\TestCase;
   use Illuminate\Foundation\Testing\DatabaseTransactions;
   
   class ExampleServiceTest extends TestCase
   {
       use DatabaseTransactions;
       
       public function test_service_method_works_correctly(): void
       {
           // Arrange
           $service = new ExampleService();
           
           // Act
           $result = $service->someMethod();
           
           // Assert
           $this->assertTrue($result);
       }
   }
   ```

2. **Feature Tests**:
   - Create new test files in `tests/Feature/`
   - Extend `Tests\TestCase`
   - Use `DatabaseTransactions` trait for database tests
   - Focus on testing entire features or endpoints

   Example:
   ```php
   <?php
   
   namespace Tests\Feature;
   
   use Tests\TestCase;
   use Illuminate\Foundation\Testing\DatabaseTransactions;
   
   class ExampleFeatureTest extends TestCase
   {
       use DatabaseTransactions;
       
       public function test_endpoint_returns_correct_response(): void
       {
           // Act
           $response = $this->get('/api/example');
           
           // Assert
           $response->assertStatus(200);
           $response->assertJsonStructure(['data']);
       }
   }
   ```

3. **Livewire Component Tests**:
   - Use Livewire's testing utilities
   - Test component rendering, events, and validation

   Example:
   ```php
   <?php
   
   namespace Tests\Feature;
   
   use App\Filament\SomeComponent;
   use Livewire\Livewire;
   use Tests\TestCase;
   
   class ComponentTest extends TestCase
   {
       public function test_component_can_render(): void
       {
           Livewire::test(SomeComponent::class)
               ->assertStatus(200);
       }
       
       public function test_component_validation(): void
       {
           Livewire::test(SomeComponent::class)
               ->set('someField', '')
               ->call('save')
               ->assertHasErrors(['someField' => 'required']);
       }
   }
   ```

### Testing Best Practices

1. **Use DatabaseTransactions**: This ensures database changes are rolled back after each test.
2. **Mock External Services**: Use mocks for external APIs and services.
3. **Test Edge Cases**: Include tests for error conditions and edge cases.
4. **Descriptive Test Names**: Use descriptive method names that explain what is being tested.
5. **Arrange-Act-Assert Pattern**: Structure tests with clear setup, action, and verification phases.

## Additional Development Information

### Code Style

The project follows PSR-12 coding standards. Code style can be checked and fixed using Laravel Pint:

```bash
# Check code style
docker compose exec app ./vendor/bin/pint --test

# Fix code style issues
docker compose exec app ./vendor/bin/pint
```

### Key Components

1. **Filament Admin Panel**:
   - The project uses Filament v3.2.* for the admin interface
   - Custom resources ADMIN panel are located in `app/Filament/`
   - Custom resources APPLICATION panel are located in `app/Filament/app/`
   - Follows the TALL stack (Tailwind, Alpine.js, Laravel, Livewire)

2. **Multi-tenancy**:
   - The application appears to support multi-tenant architecture
   - Tenant-specific operations should be isolated

3. **Queue Processing**:
   - Laravel Horizon is used for queue management
   - Queue workers run in a dedicated container
   - Monitor queues at `/horizon` endpoint (when in development)

4. **Scheduled Tasks**:
   - Cron jobs are defined in `app/Console/Kernel.php`
   - Tasks run automatically in the cron container

5. **Monitoring**:
   - Laravel Nightwatch is used for application monitoring
   - Stats service tracks various metrics

### Development Workflow

1. **Feature Development**:
   - Create a feature branch from main/master
   - Implement the feature with tests
   - Submit a pull request

2. **Database Changes**:
   - Create migrations for schema changes
   - Use seeders for reference data
   - Test migrations both up and down

3. **Debugging**:
   - Laravel logs are in `storage/logs/`
   - Docker logs can be viewed with `docker compose logs`
   - Nightwatch provides monitoring data

4. **Performance Considerations**:
   - Use Redis for caching when appropriate
   - Consider queue jobs for long-running processes
   - Monitor database query performance

### Deployment

The application appears to support different deployment environments with separate Docker Compose configurations:
- `server.docker-compose.yml`: Main server configuration / Local development
- `stage.docker-compose.yml`: Staging environment
- `replica.docker-compose.yml`: Replica/backup server

Ensure environment-specific variables are properly configured in the corresponding .env files.
